<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:ignore="RtlSymmetry">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="top|center_horizontal"
        android:focusable="false"
        android:focusableInTouchMode="false"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:clipChildren="false"
            android:clipToPadding="false"
            android:orientation="horizontal"
            android:paddingLeft="@dimen/vs_50"
            android:paddingTop="@dimen/vs_20"
            android:paddingRight="@dimen/vs_50"
            android:paddingBottom="@dimen/vs_20">

            <View
                android:layout_width="@dimen/vs_5"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:layout_marginTop="@dimen/vs_8"
                android:layout_marginEnd="@dimen/vs_12"
                android:layout_marginRight="@dimen/vs_12"
                android:layout_marginBottom="@dimen/vs_8"
                android:background="?attr/color_theme" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="@dimen/vs_50"
                android:drawablePadding="@dimen/vs_10"
                android:gravity="center"
                android:text="设置"
                android:textColor="@android:color/white"
                android:textSize="@dimen/ts_34"
                android:textStyle="bold" />
        </LinearLayout>


    </LinearLayout>

    <ScrollView
        android:overScrollMode="never"
        android:scrollbars="none"
        android:layout_width="match_parent"
        android:layout_height="match_parent"

        android:paddingRight="@dimen/vs_50"
        android:layout_marginTop="@dimen/vs_1">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingStart="@dimen/vs_60"
            android:paddingLeft="@dimen/vs_60"

            android:paddingBottom="@dimen/vs_60">

            <!--    画面比例-->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/vs_8"
                    android:layout_marginLeft="@dimen/vs_8"
                    android:layout_marginBottom="@dimen/vs_3"
                    android:text="画面比例"
                    android:textColor="@color/white"
                    android:textSize="@dimen/ts_24"
                    android:textStyle="bold" />

                <HorizontalScrollView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:scrollbars="none">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content">

                        <LinearLayout
                            android:id="@+id/setting_playeratio_default_linear"
                            style="@style/setting_menu_linear"
                            android:background="@drawable/shape_setting_model_focus"
                            android:gravity="center">

                            <ImageView
                                android:id="@+id/iv_setting_playeratio_default_linear"
                                style="@style/setting_menu_item_checked"
                                android:layout_width="@dimen/vs_30"
                                android:layout_height="@dimen/vs_30"
                                android:src="@drawable/setting_checked" />


                            <TextView
                                style="@style/setting_menu_item"
                                android:textColor="@color/white"
                                android:text="默认" />


                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/setting_playeratio_16_9_linear"
                            style="@style/setting_menu_linear"
                            android:background="@drawable/shape_setting_model_focus"
                            android:gravity="center">

                            <ImageView
                                android:id="@+id/iv_setting_playeratio_16_9_linear"
                                style="@style/setting_menu_item_checked"
                                android:layout_width="@dimen/vs_30"
                                android:layout_height="@dimen/vs_30"
                                android:src="@drawable/setting_checked" />

                            <TextView
                                style="@style/setting_menu_item"
                                android:textColor="@color/white"
                                android:text="16:9" />
                        </LinearLayout>


                        <LinearLayout
                            android:id="@+id/setting_playeratio_4_3_linear"
                            style="@style/setting_menu_linear"
                            android:background="@drawable/shape_setting_model_focus"
                            android:gravity="center">

                            <ImageView
                                android:id="@+id/iv_setting_playeratio_4_3_linear"
                                style="@style/setting_menu_item_checked"
                                android:layout_width="@dimen/vs_30"
                                android:layout_height="@dimen/vs_30"
                                android:src="@drawable/setting_checked" />

                            <TextView
                                style="@style/setting_menu_item"
                                android:textColor="@color/white"
                                android:text="4:3" />
                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/setting_playeratio_tianchong_linear"
                            style="@style/setting_menu_linear"
                            android:background="@drawable/shape_setting_model_focus"
                            android:gravity="center">

                            <ImageView
                                android:id="@+id/iv_setting_playeratio_tianchong_linear"
                                style="@style/setting_menu_item_checked"
                                android:layout_width="@dimen/vs_30"
                                android:layout_height="@dimen/vs_30"
                                android:src="@drawable/setting_checked" />

                            <TextView
                                style="@style/setting_menu_item"
                                android:textColor="@color/white"
                                android:text="填充" />
                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/setting_playeratio_yuanshi_linear"
                            style="@style/setting_menu_linear"
                            android:background="@drawable/shape_setting_model_focus"
                            android:gravity="center">

                            <ImageView
                                android:id="@+id/iv_setting_playeratio_yuanshi_linear"
                                style="@style/setting_menu_item_checked"
                                android:layout_width="@dimen/vs_30"
                                android:layout_height="@dimen/vs_30"
                                android:src="@drawable/setting_checked" />

                            <TextView
                                style="@style/setting_menu_item"
                                android:textColor="@color/white"
                                android:text="原始" />
                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/setting_playeratio_caijian_linear"
                            style="@style/setting_menu_linear"
                            android:background="@drawable/shape_setting_model_focus"
                            android:gravity="center">

                            <ImageView
                                android:id="@+id/iv_setting_playeratio_caijian_linear"
                                style="@style/setting_menu_item_checked"
                                android:layout_width="@dimen/vs_30"
                                android:layout_height="@dimen/vs_30"
                                android:src="@drawable/setting_checked" />

                            <TextView
                                style="@style/setting_menu_item"
                                android:textColor="@color/white"
                                android:text="裁剪" />
                        </LinearLayout>

                    </LinearLayout>

                </HorizontalScrollView>


            </LinearLayout>

            <!--    播放核心-->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"

                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/vs_8"
                    android:layout_marginLeft="@dimen/vs_8"
                    android:text="播放核心"
                    android:textColor="@color/white"
                    android:textSize="@dimen/ts_24"
                    android:textStyle="bold" />

                <HorizontalScrollView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:scrollbars="none">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content">

                        <LinearLayout
                            android:id="@+id/setting_playercore_system_linear"
                            style="@style/setting_menu_linear"
                            android:background="@drawable/shape_setting_model_focus"
                            android:gravity="center">

                            <ImageView
                                android:id="@+id/iv_setting_playercore_system_linear"
                                style="@style/setting_menu_item_checked"
                                android:layout_width="@dimen/vs_30"
                                android:layout_height="@dimen/vs_30"
                                android:src="@drawable/setting_checked" />

                            <TextView
                                style="@style/setting_menu_item"
                                android:textColor="@color/white"
                                android:text="系统" />
                        </LinearLayout>


                        <LinearLayout
                            android:id="@+id/setting_playercore_ijk_linear"
                            style="@style/setting_menu_linear"
                            android:background="@drawable/shape_setting_model_focus"
                            android:gravity="center">

                            <ImageView
                                android:id="@+id/iv_setting_playercore_ijk_linear"
                                style="@style/setting_menu_item_checked"
                                android:layout_width="@dimen/vs_30"
                                android:layout_height="@dimen/vs_30"
                                android:src="@drawable/setting_checked" />

                            <TextView
                                style="@style/setting_menu_item"
                                android:textColor="@color/white"
                                android:text="IJK" />
                        </LinearLayout>


                        <LinearLayout
                            android:id="@+id/setting_playercore_exo_linear"
                            style="@style/setting_menu_linear"
                            android:background="@drawable/shape_setting_model_focus"
                            android:gravity="center">

                            <ImageView
                                android:id="@+id/iv_setting_playercore_exo_linear"
                                style="@style/setting_menu_item_checked"
                                android:layout_width="@dimen/vs_30"
                                android:layout_height="@dimen/vs_30"
                                android:src="@drawable/setting_checked" />

                            <TextView
                                style="@style/setting_menu_item"
                                android:textColor="@color/white"
                                android:text="EXO" />
                        </LinearLayout>

                    </LinearLayout>

                </HorizontalScrollView>

            </LinearLayout>

            <!--    首页列表-->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/vs_8"
                    android:layout_marginLeft="@dimen/vs_8"
                    android:text="站点推荐"
                    android:textColor="@color/white"
                    android:textSize="@dimen/ts_24"
                    android:textStyle="bold" />

                <HorizontalScrollView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:fillViewport="true"
                    android:scrollbars="none">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content">

                        <LinearLayout
                            android:id="@+id/setting_llHomeRec_douBan_linear"
                            style="@style/setting_menu_linear"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:background="@drawable/shape_setting_model_focus"
                            android:gravity="center">

                            <ImageView
                                android:id="@+id/iv_setting_llHomeRec_douBan_linear"
                                style="@style/setting_menu_item_checked"
                                android:layout_width="@dimen/vs_30"
                                android:layout_height="@dimen/vs_30"
                                android:src="@drawable/setting_checked" />

                            <TextView
                                style="@style/setting_menu_item"
                                android:textColor="@color/white"
                                android:text="豆瓣热播" />
                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/setting_llHomeRec_recommend_linear"
                            style="@style/setting_menu_linear"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:background="@drawable/shape_setting_model_focus"
                            android:gravity="center">

                            <ImageView
                                android:id="@+id/iv_setting_llHomeRec_recommend_linear"
                                style="@style/setting_menu_item_checked"
                                android:layout_width="@dimen/vs_30"
                                android:layout_height="@dimen/vs_30"
                                android:src="@drawable/setting_checked" />

                            <TextView
                                style="@style/setting_menu_item"
                                android:textColor="@color/white"
                                android:text="站点推荐" />
                        </LinearLayout>


                        <LinearLayout
                            android:id="@+id/setting_llHomeRec_history_linear"
                            style="@style/setting_menu_linear"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:background="@drawable/shape_setting_model_focus"
                            android:gravity="center">

                            <ImageView
                                android:id="@+id/iv_setting_llHomeRec_history_linear"
                                style="@style/setting_menu_item_checked"
                                android:layout_width="@dimen/vs_30"
                                android:layout_height="@dimen/vs_30"
                                android:src="@drawable/setting_checked" />

                            <TextView
                                style="@style/setting_menu_item"
                                android:textColor="@color/white"
                                android:text="历史记录" />
                        </LinearLayout>

                    </LinearLayout>

                </HorizontalScrollView>

            </LinearLayout>


            <!--    播放设置-->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/vs_8"
                    android:layout_marginLeft="@dimen/vs_8"
                    android:layout_marginBottom="@dimen/vs_10"
                    android:text="播放设置"
                    android:textColor="@color/white"
                    android:textSize="@dimen/ts_24"
                    android:textStyle="bold" />

            </LinearLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/vs_60"
            android:layout_marginBottom="@dimen/vs_10"
            android:focusable="false"
            android:orientation="horizontal">
            <!--    首页数据-->

            <LinearLayout
                android:id="@+id/llHomeApi"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginStart="@dimen/vs_5"
                android:layout_marginLeft="@dimen/vs_5"
                android:layout_weight="1"
                android:background="@drawable/shape_setting_model_focus"
                android:focusable="true"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingLeft="@dimen/vs_20"
                android:paddingRight="@dimen/vs_20">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="首页数据源"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_24" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1" />

                <TextView
                    android:id="@+id/tvHomeApi"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_24" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/vs_10"
                    android:layout_marginLeft="@dimen/vs_10"
                    android:text=">"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_24" />
            </LinearLayout>
        </LinearLayout>


            <!--    开始-->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/vs_60"
                android:layout_marginBottom="@dimen/vs_10"
                android:focusable="false"
                android:orientation="horizontal">
                <!--  嗅探方式  -->
                <LinearLayout
                    android:id="@+id/llParseWebVew"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginStart="@dimen/vs_5"
                    android:layout_marginLeft="@dimen/vs_5"
                    android:layout_weight="1"
                    android:background="@drawable/shape_setting_model_focus"
                    android:focusable="true"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingLeft="@dimen/vs_20"
                    android:paddingRight="@dimen/vs_20">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="嗅探方式"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/ts_24" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1" />

                    <TextView
                        android:id="@+id/tvParseWebView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/ts_24" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/vs_10"
                        android:layout_marginLeft="@dimen/vs_10"
                        android:text=">"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/ts_24" />
                </LinearLayout>

                <!--  窗口预览  -->

                <LinearLayout
                    android:id="@+id/showPreview"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginStart="@dimen/vs_5"
                    android:layout_marginLeft="@dimen/vs_5"
                    android:layout_weight="1"
                    android:background="@drawable/shape_setting_model_focus"
                    android:focusable="true"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingLeft="@dimen/vs_20"
                    android:paddingRight="@dimen/vs_20">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="窗口预览"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/ts_24" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1" />

                    <TextView
                        android:id="@+id/showPreviewText"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/ts_24" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/vs_10"
                        android:layout_marginLeft="@dimen/vs_10"
                        android:text=">"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/ts_24" />
                </LinearLayout>

                <!--  数据源显示  -->

                <LinearLayout
                    android:id="@+id/llHomeShow"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginStart="@dimen/vs_5"
                    android:layout_marginLeft="@dimen/vs_5"
                    android:layout_weight="1"
                    android:background="@drawable/shape_setting_model_focus"
                    android:focusable="true"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingLeft="@dimen/vs_20"
                    android:paddingRight="@dimen/vs_20">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="数据源显示"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/ts_24" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1" />

                    <TextView
                        android:id="@+id/tvHomeShow"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/ts_24" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/vs_10"
                        android:layout_marginLeft="@dimen/vs_10"
                        android:text=">"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/ts_24" />
                </LinearLayout>

            </LinearLayout>


        <!--    开始-->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/vs_60"
            android:layout_marginBottom="@dimen/vs_10"
            android:focusable="false"
            android:orientation="horizontal">
            <!--  渲染方式  -->
            <LinearLayout
                android:id="@+id/llRender"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginStart="@dimen/vs_5"
                android:layout_marginLeft="@dimen/vs_5"
                android:layout_weight="1"
                android:background="@drawable/shape_setting_model_focus"
                android:focusable="true"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingLeft="@dimen/vs_20"
                android:paddingRight="@dimen/vs_20">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="渲染方式"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_24" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1" />

                <TextView
                    android:id="@+id/tvRenderType"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_24" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/vs_10"
                    android:layout_marginLeft="@dimen/vs_10"
                    android:text=">"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_24" />
            </LinearLayout>

            <!--  搜索结果  -->
            <LinearLayout
                android:id="@+id/llSearchView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginStart="@dimen/vs_5"
                android:layout_marginLeft="@dimen/vs_5"
                android:layout_weight="1"
                android:background="@drawable/shape_setting_model_focus"
                android:focusable="true"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingLeft="@dimen/vs_20"
                android:paddingRight="@dimen/vs_20">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="搜索结果"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_24" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1" />

                <TextView
                    android:id="@+id/tvSearchView"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_24" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/vs_10"
                    android:layout_marginLeft="@dimen/vs_10"
                    android:text=">"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_24" />
            </LinearLayout>

            <!--  解码方式  -->
            <LinearLayout
                android:id="@+id/llMediaCodec"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginStart="@dimen/vs_5"
                android:layout_marginLeft="@dimen/vs_5"
                android:layout_weight="1"
                android:background="@drawable/shape_setting_model_focus"
                android:focusable="true"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingLeft="@dimen/vs_20"
                android:paddingRight="@dimen/vs_20">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="解码方式"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_24" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1" />

                <TextView
                    android:id="@+id/tvMediaCodec"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_24" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/vs_10"
                    android:layout_marginLeft="@dimen/vs_10"
                    android:text=">"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_24" />
            </LinearLayout>

        </LinearLayout>


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/vs_60"
                android:layout_marginBottom="@dimen/vs_10"
                android:focusable="false"
                android:orientation="horizontal">
                <!--  渲染方式  -->
                <LinearLayout
                    android:id="@+id/ll_late"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginStart="@dimen/vs_5"
                    android:layout_marginLeft="@dimen/vs_5"
                    android:layout_weight="1"
                    android:background="@drawable/shape_setting_model_focus"
                    android:focusable="true"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingLeft="@dimen/vs_20"
                    android:paddingRight="@dimen/vs_20">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="超时时间"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/ts_24" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1" />

                    <TextView
                        android:id="@+id/tvLateTime"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/ts_24" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/vs_10"
                        android:layout_marginLeft="@dimen/vs_10"
                        android:text=">"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/ts_24" />
                </LinearLayout>

                <!--  搜索结果  -->
                <LinearLayout
                    android:visibility="invisible"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginStart="@dimen/vs_5"
                    android:layout_marginLeft="@dimen/vs_5"
                    android:layout_weight="1"
                    android:background="@drawable/shape_setting_model_focus"
                    android:focusable="true"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingLeft="@dimen/vs_20"
                    android:paddingRight="@dimen/vs_20">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="搜索结果"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/ts_24" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/ts_24" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/vs_10"
                        android:layout_marginLeft="@dimen/vs_10"
                        android:text=">"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/ts_24" />
                </LinearLayout>

                <!--  解码方式  -->
                <LinearLayout
                    android:visibility="invisible"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginStart="@dimen/vs_5"
                    android:layout_marginLeft="@dimen/vs_5"
                    android:layout_weight="1"
                    android:background="@drawable/shape_setting_model_focus"
                    android:focusable="true"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingLeft="@dimen/vs_20"
                    android:paddingRight="@dimen/vs_20">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="解码方式"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/ts_24" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/ts_24" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/vs_10"
                        android:layout_marginLeft="@dimen/vs_10"
                        android:text=">"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/ts_24" />
                </LinearLayout>

            </LinearLayout>



                <LinearLayout
                    android:id="@+id/llApi"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/vs_120"
                    android:layout_marginBottom="@dimen/vs_10"
                    android:background="@drawable/shape_setting_model_focus"
                    android:focusable="true"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:visibility="gone"
                    android:paddingLeft="@dimen/vs_20"
                    android:paddingRight="@dimen/vs_20">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:paddingTop="@dimen/vs_12"
                        android:paddingBottom="@dimen/vs_12">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="自定义配置来源"
                            android:textColor="@android:color/white"
                            android:textSize="@dimen/ts_24" />

                        <View
                            android:layout_width="wrap_content"
                            android:layout_height="@dimen/vs_8" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="您可以在这里配置自己的数据接口、如果您有的话"
                            android:textColor="@android:color/white"
                            android:textSize="@dimen/ts_22" />


                    </LinearLayout>

                    <TextView
                        android:id="@+id/tvApi"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:ellipsize="middle"
                        android:singleLine="true"
                        android:text="https://www.baidu.com"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/ts_24" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/vs_10"
                        android:layout_marginLeft="@dimen/vs_10"
                        android:text=">"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/ts_30" />
                </LinearLayout>





            <!--    ui设置-->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/vs_8"
                    android:layout_marginLeft="@dimen/vs_8"
                    android:layout_marginBottom="@dimen/vs_10"
                    android:text="UI设置"
                    android:textColor="@color/white"
                    android:textSize="@dimen/ts_24"
                    android:textStyle="bold" />

            </LinearLayout>








        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/vs_60"
            android:layout_marginBottom="@dimen/vs_10"
            android:focusable="false"
            android:orientation="horizontal">
            <!--    主题-->

            <LinearLayout
                android:id="@+id/llTheme"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginStart="@dimen/vs_5"
                android:layout_marginLeft="@dimen/vs_5"
                android:layout_weight="1"
                android:background="@drawable/shape_setting_model_focus"
                android:focusable="true"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingLeft="@dimen/vs_20"
                android:paddingRight="@dimen/vs_20">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="选择主题"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_24" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1" />

                <TextView
                    android:id="@+id/tvTheme"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_24" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/vs_10"
                    android:layout_marginLeft="@dimen/vs_10"
                    android:text=">"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_24" />
            </LinearLayout>


            <!--    换张壁纸-->

            <LinearLayout
                android:id="@+id/llWp"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginStart="@dimen/vs_5"
                android:layout_marginLeft="@dimen/vs_5"
                android:layout_weight="1"
                android:background="@drawable/shape_setting_model_focus"
                android:focusable="true"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingLeft="@dimen/vs_20"
                android:paddingRight="@dimen/vs_20">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="换张壁纸"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_24" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/vs_10"
                    android:layout_marginLeft="@dimen/vs_10"
                    android:text=">"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_24" />
            </LinearLayout>

            <!--    重置壁纸-->

            <LinearLayout
                android:id="@+id/llWpRecovery"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginStart="@dimen/vs_5"
                android:layout_marginLeft="@dimen/vs_5"
                android:layout_weight="1"
                android:background="@drawable/shape_setting_model_focus"
                android:focusable="true"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingLeft="@dimen/vs_20"
                android:paddingRight="@dimen/vs_20">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="重置壁纸"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_24" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/vs_10"
                    android:layout_marginLeft="@dimen/vs_10"
                    android:text=">"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_24" />
            </LinearLayout>



        </LinearLayout>




            <!--    更多设置-->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/vs_30"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/vs_8"
                    android:layout_marginLeft="@dimen/vs_8"
                    android:layout_marginBottom="@dimen/vs_10"
                    android:text="更多设置"
                    android:textColor="@color/white"
                    android:textSize="@dimen/ts_24"
                    android:textStyle="bold" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/vs_60"
                    android:layout_marginBottom="@dimen/vs_10"
                    android:focusable="false"
                    android:orientation="horizontal">
                    <!--    安全dns-->

                    <LinearLayout
                        android:id="@+id/llDns"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginStart="@dimen/vs_5"
                        android:layout_marginLeft="@dimen/vs_5"
                        android:layout_weight="1"
                        android:background="@drawable/shape_setting_model_focus"
                        android:focusable="true"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:paddingLeft="@dimen/vs_20"
                        android:paddingRight="@dimen/vs_20">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="安全DNS"
                            android:textColor="@android:color/white"
                            android:textSize="@dimen/ts_24" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1" />

                        <TextView
                            android:id="@+id/tvDns"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@android:color/white"
                            android:textSize="@dimen/ts_24" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/vs_10"
                            android:layout_marginLeft="@dimen/vs_10"
                            android:text=">"
                            android:textColor="@android:color/white"
                            android:textSize="@dimen/ts_24" />
                    </LinearLayout>

                    <!--    数据备份-->

                    <LinearLayout
                        android:id="@+id/llBackup"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginStart="@dimen/vs_5"
                        android:layout_marginLeft="@dimen/vs_5"
                        android:layout_weight="1"
                        android:background="@drawable/shape_setting_model_focus"
                        android:focusable="true"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:paddingLeft="@dimen/vs_20"
                        android:paddingRight="@dimen/vs_20">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="数据备份"
                            android:textColor="@android:color/white"
                            android:textSize="@dimen/ts_24" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1" />

                     <!--   <TextView
                            android:id="@+id/tvDns"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@android:color/white"
                            android:textSize="@dimen/ts_24" />-->

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/vs_10"
                            android:layout_marginLeft="@dimen/vs_10"
                            android:text=">"
                            android:textColor="@android:color/white"
                            android:textSize="@dimen/ts_24" />
                    </LinearLayout>


                    <!--    清空缓存-->

                    <LinearLayout
                        android:id="@+id/llClearCache"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginStart="@dimen/vs_5"
                        android:layout_marginLeft="@dimen/vs_5"
                        android:layout_weight="1"
                        android:background="@drawable/shape_setting_model_focus"
                        android:focusable="true"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:paddingLeft="@dimen/vs_20"
                        android:paddingRight="@dimen/vs_20">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="清空缓存"
                            android:textColor="@android:color/white"
                            android:textSize="@dimen/ts_24" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1" />

                        <!--   <TextView
                               android:id="@+id/tvDns"
                               android:layout_width="wrap_content"
                               android:layout_height="wrap_content"
                               android:textColor="@android:color/white"
                               android:textSize="@dimen/ts_24" />-->

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/vs_10"
                            android:layout_marginLeft="@dimen/vs_10"
                            android:text=">"
                            android:textColor="@android:color/white"
                            android:textSize="@dimen/ts_24" />
                    </LinearLayout>

                    <!--    关于-->

                    <LinearLayout
                        android:id="@+id/llAbout"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginStart="@dimen/vs_5"
                        android:layout_marginLeft="@dimen/vs_5"
                        android:layout_weight="1"
                        android:background="@drawable/shape_setting_model_focus"
                        android:focusable="true"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:paddingLeft="@dimen/vs_20"
                        android:paddingRight="@dimen/vs_20">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="关于"
                            android:textColor="@android:color/white"
                            android:textSize="@dimen/ts_24" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1" />

                 <!--       <TextView
                            android:id="@+id/tvDns"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@android:color/white"
                            android:textSize="@dimen/ts_24" />-->

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/vs_10"
                            android:layout_marginLeft="@dimen/vs_10"
                            android:text=">"
                            android:textColor="@android:color/white"
                            android:textSize="@dimen/ts_24" />
                    </LinearLayout>

                </LinearLayout>





                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">


                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content">

                        <LinearLayout
                            android:id="@+id/llDebug"
                            android:visibility="gone"
                            style="@style/setting_menu_linear"
                            android:background="@drawable/shape_setting_model_focus"
                            android:gravity="center">

                            <TextView
                                style="@style/setting_menu_item"
                                android:text="DEBUG" />

                            <TextView
                                android:id="@+id/tvDebugOpen"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="关"
                                android:textColor="@color/color_FFB800"
                                android:textSize="@dimen/ts_24" />
                        </LinearLayout>




                    </LinearLayout>
                </LinearLayout>
            </LinearLayout>
        </LinearLayout>
    </ScrollView>
</LinearLayout>