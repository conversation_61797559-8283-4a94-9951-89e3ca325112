<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <TextView
            android:textSize="@dimen/vs_40"
            android:textColor="@color/color_FFFFFF"
            android:layout_gravity="center"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/vs_60"
            android:text="消息中心" />
        <LinearLayout
            android:layout_gravity="center"
            android:orientation="vertical"
            android:paddingLeft="@dimen/vs_200"
            android:paddingRight="@dimen/vs_200"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="@dimen/vs_20"
            android:layout_marginBottom="@dimen/vs_80">
            <androidx.recyclerview.widget.RecyclerView
                android:layout_gravity="center"
                android:id="@+id/message_list"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:overScrollMode="never" />
        </LinearLayout>
    </LinearLayout>
</LinearLayout>
