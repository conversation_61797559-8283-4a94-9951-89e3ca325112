<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:minWidth="@dimen/vs_480"
    android:layout_height="match_parent">


    <LinearLayout
        android:layout_width="@dimen/vs_600"
        android:minWidth="@dimen/vs_480"
        android:layout_height="@dimen/vs_530"
        android:layout_gravity="center"
        android:background="@drawable/shape_dialog_delete_bg"
        android:orientation="vertical"
        android:padding="@dimen/vs_20">


        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:textColor="@color/color_FFFFFF"
            android:textSize="@dimen/ts_30"
            android:layout_height="wrap_content"/>

        <ScrollView
            android:layout_marginTop="@dimen/ts_15"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:layout_width="match_parent"
                android:orientation="vertical"
                android:layout_height="wrap_content">
                <TextView
                    android:id="@+id/tipInfo"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:lineSpacingExtra="@dimen/ts_50"
                    android:lineSpacingMultiplier="0"
                    android:textAlignment="gravity"
                    android:textColor="@color/color_FFFFFF"
                    android:textSize="@dimen/ts_24"
                    tools:text="111" />

            </LinearLayout>

        </ScrollView>

    </LinearLayout>
</FrameLayout>