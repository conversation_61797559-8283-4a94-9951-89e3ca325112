<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.github.tvbox.osc">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_MULTICAST_STATE"
        tools:ignore="LeanbackUsesWifi" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>
    <uses-permission android:name="android.permission.WRITE_SETTINGS"
        tools:ignore="ProtectedPermissions" />
    <uses-permission android:name="android.permission.GET_TASKS" />

    <application
        android:name=".base.App"
        android:allowBackup="true"
        android:banner="@drawable/app_banner"
        android:configChanges="orientation|keyboardHidden|screenSize"
        android:hardwareAccelerated="true"
        android:icon="@drawable/app_icon"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:networkSecurityConfig="@xml/network_security_config"
        android:supportsRtl="true"
        android:theme="@style/AppTheme.NoActionBar"
        android:usesCleartextTraffic="true"
        tools:targetApi="n">
        <!-- 下面两个是TalkingData统计id和渠道 -->
        <meta-data android:name="TD_APP_ID" android:value="" />
        <meta-data android:name="TD_CHANNEL_ID" android:value="" />
        <activity
            android:name=".ui.activity.StartActivity"
            android:screenOrientation="sensorLandscape">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name=".ui.activity.HomeActivity"
            android:screenOrientation="sensorLandscape" />
        <activity
            android:name=".ui.activity.LivePlayActivity"
            android:screenOrientation="sensorLandscape" />
        <activity
            android:name=".ui.activity.DetailActivity"
            android:screenOrientation="sensorLandscape" />
        <activity
            android:name=".ui.activity.PlayActivity"
            android:screenOrientation="sensorLandscape" />
        <activity
            android:name=".ui.activity.PushActivity"
            android:screenOrientation="sensorLandscape" />
        <activity
            android:name=".ui.activity.SearchActivity"
            android:screenOrientation="sensorLandscape" />
        <activity
            android:name=".ui.activity.FastSearchActivity"
            android:screenOrientation="sensorLandscape" />
        <activity
            android:name=".ui.activity.SettingActivity"
            android:screenOrientation="sensorLandscape" />
        <activity
            android:name=".ui.activity.HistoryActivity"
            android:screenOrientation="sensorLandscape" />
        <activity
            android:name=".ui.activity.CollectActivity"
            android:screenOrientation="sensorLandscape" />
        <activity
            android:name=".ui.activity.UserActivity"
            android:screenOrientation="sensorLandscape" />
        <activity
            android:name=".ui.activity.VipCardActivity"
            android:screenOrientation="sensorLandscape" />
        <activity
            android:name=".ui.activity.AboutActivity"
            android:screenOrientation="sensorLandscape" />
        <activity
            android:name=".ui.activity.VipActivity"
            android:screenOrientation="sensorLandscape" />
        <activity
            android:name=".ui.activity.KamActivity"
            android:screenOrientation="sensorLandscape" />
        <activity
            android:name=".ui.activity.WebViewActivity"
            android:screenOrientation="sensorLandscape" />
        <activity
            android:name=".ui.activity.ScreensaverActivity"
            android:screenOrientation="sensorLandscape" />
        <activity
            android:name=".ui.activity.MyBanner"
            android:screenOrientation="sensorLandscape" />
        <activity
            android:name=".ui.activity.MessageActivity"
            android:screenOrientation="sensorLandscape" />
        <activity
            android:name=".ui.activity.ExchangeActivity"
            android:screenOrientation="sensorLandscape" />
        <activity
            android:name=".ui.activity.AppsActivity"
            android:screenOrientation="sensorLandscape" />
        <activity
            android:name=".ui.activity.DxianluActivity"
            android:screenOrientation="sensorLandscape" />

        <receiver android:name=".receiver.SearchReceiver">
            <intent-filter>
                <action android:name="android.content.movie.search.Action" />
            </intent-filter>
        </receiver>
        <receiver android:name=".receiver.CustomWebReceiver">
            <intent-filter>
                <action android:name="android.content.movie.custom.web.Action" />
            </intent-filter>
        </receiver>

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <meta-data
            android:name="design_width_in_dp"
            android:value="1280" />
        <meta-data
            android:name="design_height_in_dp"
            android:value="720" />

        <meta-data
            android:name="xwalk_enable_download_mode"
            android:value="enable" />
        <meta-data
            android:name="xwalk_verify"
            android:value="disable" />
    </application>

    <uses-feature
        android:name="android.software.leanback"
        android:required="false"
        tools:ignore="ManifestOrder" />
    <uses-feature
        android:name="android.hardware.touchscreen"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.location.gps"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.bluetooth"
        android:required="false" />

</manifest>