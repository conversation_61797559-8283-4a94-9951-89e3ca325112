<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_focused="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/tv_card_bg_focus" />
            <corners android:radius="12dp" />
            <stroke android:width="2dp" android:color="@color/tv_category_focus" />
        </shape>
    </item>
    <item android:state_focused="false">
        <shape android:shape="rectangle">
            <solid android:color="@color/tv_card_bg" />
            <corners android:radius="12dp" />
        </shape>
    </item>
</selector>
