<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">


    <LinearLayout
        android:paddingLeft="20mm"
        android:layout_width="match_parent"
        android:layout_height="@dimen/vs_50"
        android:layout_marginTop="@dimen/vs_10"
        android:layout_marginBottom="@dimen/vs_10">
        <LinearLayout
            android:gravity="center"
            android:layout_width="@dimen/vs_300"
            android:layout_height="match_parent">
            <ImageView
                android:id="@+id/iv_back"
                android:background="@drawable/shape_user_search"
                android:focusable="true"
                android:layout_width="18dp"
                android:layout_height="wrap_content"
                android:src="@drawable/v_back" />


            <TextView
                android:textSize="@dimen/ts_22"
                android:textStyle="bold"
                android:textColor="@android:color/white"
                android:layout_gravity="center"
                android:id="@+id/mSearchTitle"
                android:paddingTop="@dimen/vs_10"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:text="搜索(0/0)"
                android:shadowColor="@color/color_FF000000"
                android:shadowDx="0.0"
                android:shadowDy="0.0"
                android:shadowRadius="5.0"
                android:gravity="center_horizontal"
                android:layout_marginRight="18dp"
                android:layout_weight="1.0" />
        </LinearLayout>
        <com.owen.tvrecyclerview.widget.TvRecyclerView
            android:id="@+id/mGridViewWordFenci"
            android:layout_width="match_parent"
            android:layout_height="@dimen/vs_50"
            android:clipChildren="false"
            android:clipToPadding="false"
            app:tv_horizontalSpacingWithMargins="@dimen/vs_5"
            app:tv_selectedItemIsCentered="true"
            app:tv_verticalSpacingWithMargins="@dimen/vs_5" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal">



        <LinearLayout
            android:orientation="vertical"
            android:id="@+id/llWord"
            android:paddingLeft="20mm"
            android:paddingBottom="@dimen/vs_20"
            android:layout_width="@dimen/vs_300"
            android:layout_height="match_parent">
            <com.owen.tvrecyclerview.widget.TvRecyclerView
                android:id="@+id/mGridViewWord"
                android:focusable="true"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:tv_horizontalSpacingWithMargins="@dimen/vs_5"
                app:tv_selectedItemIsCentered="true"
                app:tv_verticalSpacingWithMargins="@dimen/vs_5" />
        </LinearLayout>


        <LinearLayout
            android:id="@+id/llLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:paddingLeft="20mm"
            android:paddingBottom="@dimen/vs_20"
            android:paddingRight="20mm" >


            <com.owen.tvrecyclerview.widget.TvRecyclerView
                android:id="@+id/mGridView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:visibility="invisible"
                app:tv_horizontalSpacingWithMargins="@dimen/vs_5"
                app:tv_selectedItemIsCentered="true"
                app:tv_verticalSpacingWithMargins="@dimen/vs_5" />

            <com.owen.tvrecyclerview.widget.TvRecyclerView
                android:id="@+id/mGridViewFilter"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:visibility="invisible"
                app:tv_horizontalSpacingWithMargins="@dimen/vs_5"
                app:tv_selectedItemIsCentered="true"
                app:tv_verticalSpacingWithMargins="@dimen/vs_5" />


        </LinearLayout>
    </LinearLayout>
</LinearLayout>