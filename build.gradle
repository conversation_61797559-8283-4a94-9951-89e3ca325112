// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    repositories {
        gradlePluginPortal()
        google()
        mavenCentral()
        maven { url "https://jitpack.io" }
//        maven { url 'https://download.01.org/crosswalk/releases/crosswalk/android/maven2' }
        maven { url "https://s01.oss.sonatype.org/content/groups/public" }
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:7.4.2'

        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

allprojects {
    repositories {
        gradlePluginPortal()
        google()
        mavenCentral()
        maven { url "https://jitpack.io" }
//        maven { url 'https://download.01.org/crosswalk/releases/crosswalk/android/maven2' }
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}