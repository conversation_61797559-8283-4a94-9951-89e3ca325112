<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/black">


    <com.github.tvbox.osc.player.MyVideoView
        android:id="@+id/mVideoView"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <ProgressBar
        android:id="@+id/play_loading"
        android:layout_width="@dimen/vs_50"
        android:layout_height="@dimen/vs_50"
        android:layout_gravity="center"
        android:indeterminateBehavior="repeat"
        android:indeterminateDrawable="@drawable/anim_loading"
        android:indeterminateTint="?attr/color_theme_70"
        android:indeterminateOnly="true" />

    <ImageView
        android:id="@+id/play_load_error"
        android:layout_width="@dimen/vs_50"
        android:layout_height="@dimen/vs_50"
        android:layout_gravity="center"
        android:alpha="0.75"
        android:focusable="false"
        android:focusableInTouchMode="false"
        android:src="@drawable/icon_error"
        android:visibility="gone"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/play_load_tip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginTop="@dimen/vs_50"
        android:text="正在加载中"
        android:textColor="@color/color_CCFFFFFF"
        android:textSize="@dimen/ts_24" />
</FrameLayout>