apply plugin: 'com.android.application'

android {
    compileSdkVersion 33

    defaultConfig {
        applicationId 'com.huikan.cc'
        minSdkVersion 16
        targetSdkVersion 26
        versionCode 1
        versionName '4.6.8'
        multiDexEnabled true
        //设置room的Schema的位置
        javaCompileOptions {
            annotationProcessorOptions {
                arguments = ["room.schemaLocation": "$projectDir/schemas".toString()]
            }
        }
    }

    packagingOptions {
        exclude 'META-INF/DEPENDENCIES'
    }

    android.applicationVariants.all { variant ->
        variant.outputs.each { output ->
            if (output.outputFileName != null && output.outputFileName.endsWith('.apk')) {
                def fileName
                fileName = "huikan_${variant.versionName}.apk"
                output.outputFileName = fileName
            }
        }
    }

//    signingConfigs {
//        config {
//            storeFile file('D:\\StudioProjects\\chacha520.jks')
//            storePassword 'chacha520'
//            keyAlias = 'chacha520'
//            keyPassword 'chacha520'
//        }
//    }

    buildTypes {
        debug {
            minifyEnabled false
//            signingConfig signingConfigs.config
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            ndk {
                abiFilters 'armeabi-v7a'
            }
        }
        release {
            minifyEnabled true //开启混淆
            shrinkResources true // 移除无用的resource文件
            zipAlignEnabled true //Zipalign优化
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            ndk {
                abiFilters 'armeabi-v7a'
            }
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    lintOptions {
        checkReleaseBuilds false
        abortOnError false
    }

    dexOptions {
        javaMaxHeapSize "4g"
        additionalParameters += '--multi-dex'
        additionalParameters += '--set-max-idx-number=48000'
        additionalParameters += '--minimal-main-dex'
    }
}

dependencies {
    api fileTree(dir: "libs", include: ["*.jar"])
    implementation 'com.google.zxing:core:3.4.1'

    implementation 'androidx.recyclerview:recyclerview:1.1.0'
    implementation project(":player")
    implementation project(":quickjs")

    annotationProcessor 'androidx.room:room-compiler:2.3.0'
    implementation 'androidx.room:room-runtime:2.3.0'
    implementation 'androidx.multidex:multidex:2.0.1'
    implementation 'com.squareup.okhttp3:okhttp:3.12.1'
    implementation 'com.squareup.okio:okio:2.6.0'
    implementation 'com.github.CymChad:BaseRecyclerViewAdapterHelper:2.9.45-androidx'
    implementation 'com.kingja.loadsir:loadsir:1.3.8'
    implementation 'com.google.code.gson:gson:2.8.7'
    implementation 'com.squareup.picasso:picasso:2.71828'
    implementation 'org.apache.commons:commons-text:1.7'
    implementation 'me.jessyan:autosize:1.2.1'
    implementation('com.thoughtworks.xstream:xstream:1.4.15') {
        exclude group: 'xmlpull', module: 'xmlpull'
    }
    implementation 'org.greenrobot:eventbus:3.2.0'
    implementation 'com.orhanobut:hawk:2.0.1'
    implementation 'com.lzy.net:okgo:3.0.4'
    implementation 'com.owen:tv-recyclerview:3.0.0'
    implementation 'com.github.getActivity:XXPermissions:13.6'

    //工具箱/不需要混淆？
    implementation 'com.blankj:utilcodex:1.26.0'
    implementation 'com.tencent:mmkv:1.2.13'
    implementation "cz.msebera.android:httpclient:4.4.1.2"

    //GIF空间
    implementation 'com.github.bumptech.glide:glide:4.13.2'
    annotationProcessor 'com.github.bumptech.glide:compiler:4.13.2'
    implementation 'pl.droidsonroids.gif:android-gif-drawable:1.2.2'
    implementation 'com.youth.banner:banner:1.4.10'
   // implementation 'com.baidu.mobstat:mtj-sdk:latest.integration'

    implementation 'org.nanohttpd:nanohttpd:2.3.1'
    implementation 'androidx.appcompat:appcompat:1.3.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.0.4'
    implementation 'androidx.lifecycle:lifecycle-extensions:2.2.0'
    implementation 'androidx.recyclerview:recyclerview:1.1.0'
 //   implementation files('libs\\thunder.jar')
  //  implementation project(":player")

    annotationProcessor 'androidx.room:room-compiler:2.5.2'
    implementation 'androidx.room:room-runtime:2.5.2'
    implementation 'androidx.multidex:multidex:2.0.1'
    implementation 'com.squareup.okhttp3:okhttp:3.12.1'
    implementation 'com.squareup.okio:okio:2.6.0'
    implementation 'com.github.CymChad:BaseRecyclerViewAdapterHelper:2.9.45-androidx'
    implementation 'com.kingja.loadsir:loadsir:1.3.8'
    implementation 'com.google.code.gson:gson:2.8.7'
    implementation 'com.squareup.picasso:picasso:2.71828'
    implementation 'me.jessyan:autosize:1.2.1'
    implementation('com.thoughtworks.xstream:xstream:1.4.15') {
        exclude group: 'xmlpull', module: 'xmlpull'
    }
    implementation 'org.greenrobot:eventbus:3.2.0'
    implementation 'com.orhanobut:hawk:2.0.1'



//    implementation('org.xwalk:xwalk_shared_library:23.53.589.4') {
//        exclude group: 'com.android.support'
//    }
    implementation 'com.lzy.net:okgo:3.0.4'
    // implementation 'com.tencent.bugly:crashreport_upgrade:latest.release'
    implementation 'com.owen:tv-recyclerview:3.0.0'

    implementation 'com.github.getActivity:XXPermissions:13.6'
    implementation 'org.jsoup:jsoup:1.14.1'
    implementation 'com.github.hedzr:android-file-chooser:v1.2.0-final'
    implementation 'commons-io:commons-io:2.11.0'

    implementation 'com.github.ydstar:loadingdialog:1.0.0'

    implementation files('libs\\TalkingdataSDK.jar')//统计
}