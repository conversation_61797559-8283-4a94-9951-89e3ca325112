<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/toast_layout_root"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/smtv_toast_bg"
    android:orientation="horizontal"
    android:paddingBottom="@dimen/vs_5"
    android:paddingLeft="@dimen/vs_20"
    android:paddingRight="@dimen/vs_20"
    android:paddingTop="@dimen/vs_5" >

    <ImageView
        android:id="@+id/iv_smtv_toast"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical" />

    <TextView
        android:id="@+id/tv_smtv_toast"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:textColor="@color/white"
        android:textSize="@dimen/vs_24" />

</LinearLayout>