<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:id="@+id/root"
        android:layout_width="@dimen/vs_480"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:layout_margin="@dimen/vs_50"
        android:background="@drawable/shape_dialog_bg_main"
        android:orientation="vertical"
        android:padding="@dimen/vs_25">


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingBottom="@dimen/vs_20"
            android:weightSum="2">

            <LinearLayout
                android:id="@+id/checkAll"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginStart="@dimen/vs_5"
                android:layout_marginLeft="@dimen/vs_5"
                android:layout_marginEnd="@dimen/vs_5"
                android:layout_marginRight="@dimen/vs_5"
                android:layout_weight="1"
                android:background="@drawable/button_dialog_main"
                android:focusable="true"
                android:gravity="center"
                android:orientation="horizontal"
                android:padding="@dimen/vs_10">

                <ImageView
                    android:layout_width="@dimen/vs_30"
                    android:layout_height="@dimen/vs_30"
                    android:layout_gravity="center"
                    android:layout_marginEnd="@dimen/vs_10"
                    android:layout_marginRight="@dimen/vs_10"
                    android:src="@drawable/shape_search_checkall" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="false"
                    android:layout_gravity="center"
                    android:focusable="false"
                    android:focusableInTouchMode="false"
                    android:gravity="center"
                    android:text="全选"
                    android:textColor="@color/color_FFFFFF"
                    android:textSize="@dimen/ts_20" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/clearAll"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginStart="@dimen/vs_5"
                android:layout_marginLeft="@dimen/vs_5"
                android:layout_marginEnd="@dimen/vs_5"
                android:layout_marginRight="@dimen/vs_5"
                android:layout_weight="1"
                android:background="@drawable/button_dialog_main"
                android:focusable="true"
                android:gravity="center"
                android:orientation="horizontal"
                android:padding="@dimen/vs_10">

                <ImageView
                    android:layout_width="@dimen/vs_30"
                    android:layout_height="@dimen/vs_30"
                    android:layout_gravity="center"
                    android:layout_marginEnd="@dimen/vs_10"
                    android:layout_marginRight="@dimen/vs_10"
                    android:src="@drawable/shape_search_clearall" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="false"
                    android:layout_gravity="center"
                    android:focusable="false"
                    android:focusableInTouchMode="false"
                    android:gravity="center"
                    android:text="全不选"
                    android:textColor="@color/color_FFFFFF"
                    android:textSize="@dimen/ts_20" />
            </LinearLayout>
        </LinearLayout>

        <com.owen.tvrecyclerview.widget.TvRecyclerView
            android:id="@+id/mGridView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical" />

    </LinearLayout>

</FrameLayout>