<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <TextView
            android:textSize="@dimen/vs_40"
            android:textColor="@color/color_FFFFFF"
            android:layout_gravity="center"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/vs_30"
            android:text="线路选择" />



        <LinearLayout
            android:layout_gravity="center"
            android:orientation="vertical"
            android:paddingLeft="@dimen/vs_100"
            android:paddingRight="@dimen/vs_100"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="@dimen/vs_20"
            android:layout_marginBottom="@dimen/vs_80">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/vs_40">
            <TextView
                android:textSize="@dimen/ts_20"
                android:textColor="@color/color_FFFFFF"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/vs_10"
                android:text="如果配置失败可尝试切换其他仓库===&gt;" />
            <TextView
                android:textSize="@dimen/ts_20"
                android:textColor="@color/color_theme"
                android:id="@+id/tv_resetting"
                android:background="@drawable/shape_setting_model_focus_new"
                android:focusable="true"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/vs_10"
                android:text="恢复默认" />
            </LinearLayout>


            <androidx.recyclerview.widget.RecyclerView
                android:layout_gravity="center"
                android:id="@+id/dxianlu_list"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:overScrollMode="never" />
        </LinearLayout>
    </LinearLayout>
</LinearLayout>