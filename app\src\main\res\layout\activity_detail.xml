<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <LinearLayout
        android:orientation="vertical"
        android:id="@+id/llLayout"
        android:layout_width="fill_parent"
        android:layout_height="fill_parent">

        <!--调整顶部左右边距
       android:paddingLeft="@dimen/vs_20"
       android:paddingRight="@dimen/vs_20"
       调底下的，不是调这个备注信息
       -->

        <LinearLayout
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingLeft="@dimen/vs_50"
            android:paddingRight="@dimen/vs_50"
            android:paddingBottom="@dimen/vs_10"
            android:layout_width="fill_parent"
            android:layout_height="@dimen/vs_60"
            android:layout_marginTop="@dimen/vs_10"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/tv_finishHome"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/vs_40"
                android:background="@drawable/shape_setting_model_focus"
                android:paddingLeft="@dimen/vs_6"
                android:paddingTop="@dimen/vs_2"
                android:paddingRight="@dimen/vs_6"
                android:paddingBottom="@dimen/vs_2"
                android:focusable="true"
                android:gravity="center"
                android:nextFocusDown="@+id/tvPlay"
                android:text="返回 "
                android:textSize="@dimen/ts_24"
                android:textColor="@color/color_FFFFFF"
                android:drawablePadding="@dimen/vs_5"
                app:drawableLeftCompat="@drawable/ic_home_99" />

            <TextView
                android:id="@+id/llSearch"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/vs_40"
                android:layout_marginLeft="@dimen/vs_20"
                android:background="@drawable/shape_setting_model_focus"
                android:paddingLeft="@dimen/vs_6"
                android:paddingTop="@dimen/vs_2"
                android:paddingRight="@dimen/vs_6"
                android:paddingBottom="@dimen/vs_2"
                android:focusable="true"
                android:gravity="center"
                android:nextFocusDown="@+id/tvPlay"
                android:text="搜索 "
                android:textSize="@dimen/ts_24"
                android:textColor="@color/color_FFFFFF"
                android:drawablePadding="@dimen/vs_5"
                app:drawableLeftCompat="@drawable/ic_sousuo_99" />

            <TextView
                android:id="@+id/tvUserHome"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/vs_40"
                android:layout_marginLeft="@dimen/vs_20"
                android:background="@drawable/shape_setting_model_focus"
                android:paddingLeft="@dimen/vs_6"
                android:paddingTop="@dimen/vs_2"
                android:paddingRight="@dimen/vs_6"
                android:paddingBottom="@dimen/vs_2"
                android:focusable="true"
                android:gravity="center"
                android:nextFocusDown="@+id/tvPlay"
                android:text="我的"
                android:textSize="@dimen/ts_24"
                android:textColor="@color/color_FFFFFF"
                android:drawablePadding="@dimen/vs_5"
                app:drawableLeftCompat="@drawable/icon_user" />

            <LinearLayout
                android:gravity="center"
                android:id="@+id/gongGao_root"
                android:background="@drawable/shape_setting_model_focus88"
                android:paddingLeft="@dimen/vs_15"
                android:paddingTop="@dimen/vs_3"
                android:paddingRight="@dimen/vs_15"
                android:paddingBottom="@dimen/vs_3"
                android:nextFocusDown="@id/tvPlay"
                android:layout_width="@dimen/vs_420"
                android:layout_height="@dimen/vs_40"
                android:layout_marginLeft="@dimen/vs_20">

                <ImageView
                    android:gravity="center"
                    android:layout_width="@dimen/vs_30"
                    android:layout_height="@dimen/vs_30"
                    android:src="@drawable/ic_gonggao" />

                <TextView
                    android:textSize="@dimen/ts_18"
                    android:textColor="#fffeba98"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/vs_5"
                    android:text="最新消息" />

                <View
                    android:background="@color/color_3D3D3D"
                    android:layout_width="1.0dip"
                    android:layout_height="fill_parent"
                    android:layout_marginLeft="@dimen/vs_5"
                    android:layout_marginTop="@dimen/vs_8"
                    android:layout_marginRight="@dimen/vs_5"
                    android:layout_marginBottom="@dimen/vs_8" />

                <com.github.tvbox.osc.ui.tv.widget.AlwaysMarqueeTextView
                    android:id="@+id/gonggao"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:ellipsize="marquee"
                    android:gravity="left|center_vertical"
                    android:marqueeRepeatLimit="marquee_forever"
                    android:paddingLeft="0dp"
                    android:paddingRight="0dp"
                    android:singleLine="true"
                    android:text=""
                    android:textColor="#fffeba98"
                    android:textSize="@dimen/ts_24" />

            </LinearLayout>

            <LinearLayout
                android:gravity="right"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:textSize="@dimen/ts_22"
                    android:textStyle="bold"
                    android:textColor="@color/color_FFFFFF"
                    android:gravity="center|right"
                    android:id="@+id/tvDate"
                    android:focusable="false"
                    android:focusableInTouchMode="false"
                    android:layout_width="wrap_content"
                    android:layout_height="fill_parent"
                    android:layout_marginLeft="@dimen/vs_8"
                    android:layout_marginRight="@dimen/vs_8" />

                <TextView
                    android:id="@+id/tvAppName"
                    android:textSize="@dimen/ts_22"
                    android:textStyle="bold"
                    android:textColor="@color/color_FFFFFF"
                    android:gravity="center|left"
                    android:focusable="false"
                    android:focusableInTouchMode="false"
                    android:nextFocusDown="@id/mGridViewCategory"
                    android:clickable="false"
                    android:layout_width="wrap_content"
                    android:layout_height="fill_parent"
                    android:layout_marginLeft="@dimen/vs_8"
                    android:text="@string/app_name" />

            </LinearLayout>

        </LinearLayout>

        <View
            android:background="@color/color_6CFFFFFF"
            android:layout_width="fill_parent"
            android:layout_height="@dimen/vs_1"
            android:layout_marginTop="@dimen/vs_1"
            android:layout_marginBottom="@dimen/vs_3" />

        <LinearLayout
            android:orientation="horizontal"
            android:id="@+id/topLayout"
            android:paddingLeft="@dimen/vs_50"
            android:paddingRight="@dimen/vs_50"
            android:paddingBottom="@dimen/vs_10"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/vs_20">

            <ImageView
                android:id="@+id/ivThumb"
                android:focusable="false"
                android:clickable="true"
                android:layout_width="@dimen/vs_220"
                android:layout_height="@dimen/vs_280"
                android:scaleType="fitXY" />

            <View
                android:id="@+id/previewPlayerPlace"
                android:visibility="gone"
                android:layout_width="@dimen/vs_480"
                android:layout_height="@dimen/vs_280" />

            <LinearLayout
                android:orientation="vertical"
                android:layout_width="fill_parent"
                android:layout_height="@dimen/vs_280"
                android:layout_marginLeft="@dimen/vs_10">

                <TextView
                    android:id="@+id/tvName"
                    android:textSize="@dimen/ts_28"
                    android:textStyle="bold"
                    android:textColor="@android:color/white"
                    android:ellipsize="end"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/vs_5"
                    android:text="少年歌行"
                    android:maxLines="1"
                    android:singleLine="true" />

                <LinearLayout
                    android:orientation="horizontal"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/vs_8"
                    android:layout_marginBottom="@dimen/vs_5">

                    <TextView
                        android:textSize="@dimen/ts_18"
                        android:textColor="@android:color/white"
                        android:gravity="center"
                        android:background="?attr/color_theme"
                        android:paddingLeft="@dimen/vs_6"
                        android:paddingTop="@dimen/vs_2"
                        android:paddingRight="@dimen/vs_6"
                        android:paddingBottom="@dimen/vs_2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="更新状态:" />

                    <TextView
                        android:id="@+id/tvNote"
                        android:textSize="@dimen/ts_18"
                        android:textColor="@android:color/white"
                        android:gravity="center|left"
                        android:layout_width="fill_parent"
                        android:layout_height="fill_parent"
                        android:layout_marginLeft="@dimen/vs_10"
                        android:text="更新至25集 共40集" />


                </LinearLayout>

                <LinearLayout
                    android:orientation="horizontal"
                    android:visibility="gone"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/vs_5" >

                    <TextView
                        android:textSize="@dimen/ts_18"
                        android:textColor="@color/color_FFFFFF"
                        android:ellipsize="end"
                        android:id="@id/tvSite"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginRight="@dimen/vs_10"
                        android:maxLines="1"
                        android:singleLine="true" />

                    <TextView
                        android:textSize="@dimen/ts_18"
                        android:textColor="@color/color_FFFFFF"
                        android:ellipsize="end"
                        android:id="@id/tvYear"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginRight="@dimen/vs_10"
                        android:maxLines="1"
                        android:singleLine="true" />

                    <TextView
                        android:id="@+id/tvLang"
                        android:textSize="@dimen/ts_18"
                        android:textColor="@color/color_FFFFFF"
                        android:ellipsize="end"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginRight="@dimen/vs_10"
                        android:maxLines="1"
                        android:singleLine="true" />

                    <TextView
                        android:id="@+id/tvType"
                        android:textSize="@dimen/ts_18"
                        android:textColor="@color/color_FFFFFF"
                        android:ellipsize="end"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginRight="@dimen/vs_10"
                        android:maxLines="1"
                        android:singleLine="true" />

                </LinearLayout>

                <TextView
                    android:id="@+id/tvDirector"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/vs_5"
                    android:ellipsize="end"
                    android:singleLine="true"
                    android:textColor="@color/color_FFFFFF"
                    android:textSize="@dimen/ts_18"
                    android:visibility="gone" />

                <LinearLayout
                    android:id="@+id/ll_detail"
                    android:orientation="vertical"
                    android:background="@drawable/shape_setting_model_focus"
                    android:paddingLeft="@dimen/vs_6"
                    android:paddingTop="@dimen/vs_15"
                    android:paddingRight="@dimen/vs_6"
                    android:paddingBottom="@dimen/vs_15"
                    android:focusable="true"
                    android:nextFocusUp="@+id/tvUserHome"
                    android:nextFocusDown="@+id/tvPlay"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/vs_8">

                    <TextView
                        android:id="@+id/tvActor"
                        android:textSize="@dimen/ts_18"
                        android:textColor="@color/color_FFFFFF"
                        android:ellipsize="end"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginRight="@dimen/vs_10"
                        android:text="李宏毅 刘学义 林博洋 奥润鹏 李欣泽 戴燕妮"
                        android:maxLines="1"
                        android:singleLine="true" />

                    <TextView
                        android:id="@+id/tvDes"
                        android:textSize="@dimen/ts_18"
                        android:textColor="@color/color_FFFFFF"
                        android:ellipsize="end"
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:text="身着千金裘却连客栈维修部都没钱的抠门老板萧琴与初..."
                        android:maxLines="1"
                        android:lineSpacingMultiplier="1.2" />
                </LinearLayout>

                <LinearLayout
                    android:orientation="horizontal"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/vs_8">

                    <TextView
                        android:id="@+id/tvPlay"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginRight="@dimen/vs_10"
                        android:background="@drawable/shape_setting_model_focus"
                        android:padding="@dimen/vs_10"
                        android:gravity="center"
                        android:focusable="true"
                        android:nextFocusLeft="@+id/tvGoVIP"
                        android:text="全屏播放"
                        android:textSize="@dimen/vs_15"
                        android:textColor="@android:color/white"
                        android:drawablePadding="@dimen/vs_5"
                        app:drawableTopCompat="@drawable/ic_kquanpin" />


                    <TextView
                        android:visibility="gone"
                        android:id="@+id/tv_load_next"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginRight="@dimen/vs_10"
                        android:background="@drawable/shape_setting_model_focus"
                        android:padding="@dimen/vs_10"
                        android:focusable="true"
                        android:clickable="true"
                        android:gravity="center"
                        android:text="换源"
                        android:textSize="@dimen/vs_15"
                        android:textColor="@android:color/white"
                        android:drawablePadding="@dimen/vs_5"
                        app:drawableTopCompat="@drawable/ic_shoucang" />

                    <TextView
                        android:id="@+id/tvQuickSearch"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginRight="@dimen/vs_10"
                        android:background="@drawable/shape_setting_model_focus"
                        android:padding="@dimen/vs_10"
                        android:focusable="true"
                        android:gravity="center"
                        android:text="快速搜索"
                        android:textSize="@dimen/vs_15"
                        android:textColor="@android:color/white"
                        android:drawablePadding="@dimen/vs_5"
                        app:drawableTopCompat="@drawable/ic_ksousuo" />

                    <TextView
                        android:id="@+id/tvSort"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginRight="@dimen/vs_10"
                        android:background="@drawable/shape_setting_model_focus"
                        android:padding="@dimen/vs_10"
                        android:focusable="true"
                        android:gravity="center"
                        android:text="倒叙显示"
                        android:textSize="@dimen/vs_15"
                        android:textColor="@android:color/white"
                        android:drawablePadding="@dimen/vs_5"
                        app:drawableTopCompat="@drawable/ic_daoxu" />

                    <TextView
                        android:id="@+id/tvCollect"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginRight="@dimen/vs_10"
                        android:background="@drawable/shape_setting_model_focus"
                        android:padding="@dimen/vs_10"
                        android:focusable="true"
                        android:gravity="center"
                        android:text="加入收藏"
                        android:textSize="@dimen/vs_15"
                        android:textColor="@android:color/white"
                        android:drawablePadding="@dimen/vs_5"
                        app:drawableTopCompat="@drawable/ic_shoucang" />


                    <TextView
                        android:visibility="gone"
                        android:id="@+id/autoChange"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginRight="@dimen/vs_10"
                        android:background="@drawable/shape_setting_model_focus"
                        android:padding="@dimen/vs_10"
                        android:focusable="true"
                        android:gravity="center"
                        android:text="自动换源"
                        android:textSize="@dimen/vs_15"
                        android:textColor="@android:color/white"
                        android:drawablePadding="@dimen/vs_5"
                        app:drawableTopCompat="@drawable/ic_shoucang" />

                    <TextView

                        android:id="@+id/tvGoVIP"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@drawable/shape_setting_model_focus"
                        android:padding="@dimen/vs_10"
                        android:focusable="true"
                        android:gravity="center"
                        android:nextFocusRight="@id/tvPlay"
                        android:text="开通会员"
                        android:textSize="@dimen/vs_15"
                        android:textColor="@android:color/white"
                        android:drawablePadding="@dimen/vs_5"
                        app:drawableTopCompat="@drawable/ic_kaitongvip" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/mEmptyPlaylist"
            android:gravity="center_horizontal"
            android:layout_gravity="center"
            android:orientation="horizontal"
            android:layout_width="fill_parent"
            android:layout_height="fill_parent">

            <ImageView
                android:layout_gravity="center"
                android:layout_width="@dimen/vs_128"
                android:layout_height="@dimen/vs_128"
                android:src="@drawable/icon_empty" />

            <TextView
                android:textSize="@dimen/ts_24"
                android:textColor="@android:color/white"
                android:gravity="center"
                android:layout_gravity="center"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/vs_10"
                android:text="暂无播放数据" />

        </LinearLayout>


        <com.owen.tvrecyclerview.widget.TvRecyclerView
            android:id="@+id/mGridSearch"
            android:layout_width="match_parent"
            android:layout_height="@dimen/vs_45"
            android:layout_marginLeft="@dimen/vs_50"
            android:layout_marginTop="@dimen/vs_10"
            android:layout_marginRight="@dimen/vs_50"
            android:clipChildren="false"
            android:clipToPadding="false"
            app:tv_horizontalSpacingWithMargins="@dimen/vs_10"
            app:tv_selectedItemIsCentered="true" />


        <LinearLayout
            android:orientation="horizontal"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/vs_50"
            android:layout_marginTop="@dimen/vs_10"
            android:layout_marginRight="@dimen/vs_50">

            <TextView
                android:textSize="@dimen/ts_26"
                android:textColor="@color/color_FFFFFF"
                android:gravity="center"
                android:layout_gravity="center"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/vs_45"
                android:text="视频来源：" />

            <com.owen.tvrecyclerview.widget.TvRecyclerView
                android:id="@+id/mGridViewFlag"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:layout_width="fill_parent"
                android:layout_height="@dimen/vs_45"
                app:tv_horizontalSpacingWithMargins="@dimen/vs_10"
                app:tv_selectedItemIsCentered="true" />

            <com.owen.tvrecyclerview.widget.TvRecyclerView
                android:id="@+id/mSeriesGroupView"
                android:layout_width="match_parent"
                android:layout_height="@dimen/vs_45"
                android:layout_marginLeft="@dimen/vs_50"
                android:layout_marginTop="@dimen/vs_10"
                android:layout_marginRight="@dimen/vs_50"
                android:clipChildren="false"
                android:clipToPadding="false"
                app:tv_horizontalSpacingWithMargins="@dimen/vs_10"
                app:tv_selectedItemIsCentered="true" />

        </LinearLayout>

        <com.owen.tvrecyclerview.widget.TvRecyclerView
            android:id="@+id/mGridView"
            android:clipChildren="false"
            android:clipToPadding="false"
            android:layout_width="fill_parent"
            android:layout_height="fill_parent"
            android:layout_marginLeft="@dimen/vs_50"
            android:layout_marginTop="@dimen/vs_10"
            android:layout_marginRight="@dimen/vs_50"
            android:layout_marginBottom="@dimen/vs_30"
            app:tv_horizontalSpacingWithMargins="@dimen/vs_10"
            app:tv_selectedItemIsCentered="true"
            app:tv_verticalSpacingWithMargins="@dimen/vs_10" />
    </LinearLayout>

    <androidx.fragment.app.FragmentContainerView
        android:id="@+id/previewPlayer"
        android:visibility="gone"
        android:layout_width="@dimen/vs_480"
        android:layout_height="@dimen/vs_280"
        android:layout_marginLeft="@dimen/vs_50"
        android:layout_marginTop="@dimen/vs_85" />

    <View
        android:id="@+id/previewPlayerBlock"
        android:background="@drawable/button_detail_preview"
        android:focusable="true"
        android:visibility="gone"
        android:nextFocusRight="@id/tvPlay"
        android:layout_width="@dimen/vs_480"
        android:layout_height="@dimen/vs_280"
        android:layout_marginLeft="@dimen/vs_50"
        android:layout_marginTop="@dimen/vs_85" />
</FrameLayout>