<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_focused="true">
        <shape android:shape="rectangle">
            <gradient
                android:angle="0"
                android:startColor="@color/tv_category_focus"
                android:endColor="@color/tv_gradient_middle"
                android:type="linear" />
            <corners android:radius="25dp" />
        </shape>
    </item>
    <item android:state_focused="false">
        <shape android:shape="rectangle">
            <solid android:color="@color/tv_category_bg" />
            <corners android:radius="25dp" />
            <stroke android:width="1dp" android:color="@color/tv_text_secondary" />
        </shape>
    </item>
</selector>
