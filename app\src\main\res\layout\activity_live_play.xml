<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/live_root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_gravity="center_horizontal"
    android:layout_margin="0dp"
    android:orientation="horizontal"
    android:padding="0dp">

    <xyz.doikki.videoplayer.player.VideoView
        android:id="@+id/mVideoView"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <LinearLayout
        android:id="@+id/tvLeftChannnelListLayout"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_margin="@dimen/vs_20"
        android:background="@drawable/bg_live"
        android:baselineAligned="false"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="top|center_vertical"
            android:orientation="horizontal">

            <com.owen.tvrecyclerview.widget.TvRecyclerView
                android:id="@+id/mGroupGridView"
                android:layout_width="@dimen/vs_200"
                android:layout_height="wrap_content"
                android:divider="@null"
                android:fadeScrollbars="false"
                android:listSelector="@drawable/item_bg_selector_left"
                android:padding="@dimen/vs_20"
                android:scrollbars="none"
                app:tv_selectedItemIsCentered="true"
                app:tv_verticalSpacingWithMargins="10mm" />

        </LinearLayout>


        <LinearLayout
            android:id="@+id/divLoadEpgleft"
            android:layout_width="30dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:background="@drawable/shape_live_select"
            android:clickable="true"
            android:focusable="true"
            android:onClick="divLoadEpgL"
            android:orientation="vertical"
            android:paddingTop="@dimen/vs_15"
            android:paddingBottom="@dimen/vs_15"
            android:visibility="gone">

            <TextView
                android:id="@+id/tv_arrow0"
                android:layout_width="20dp"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:gravity="center"
                android:paddingBottom="5dp"
                android:shadowColor="@color/color_000000_80"
                android:shadowDx="0"
                android:shadowDy="0"
                android:shadowRadius="3"
                android:text="更 多"
                android:textColor="@color/color_FFFFFF"
                android:textSize="@dimen/ts_18"
                android:textStyle="bold" />

            <ImageView
                android:id="@+id/iv_arrow2"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_gravity="center"
                android:gravity="center"
                android:padding="@dimen/vs_4"
                android:src="@drawable/scrollviewleft" />

            <TextView
                android:id="@+id/tv_arrow3"
                android:layout_width="20dp"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:gravity="center"
                android:paddingTop="5dp"
                android:shadowColor="@color/color_000000_80"
                android:shadowDx="0"
                android:shadowDy="0"
                android:shadowRadius="3"
                android:text="节 目"
                android:textColor="@color/color_FFFFFF"
                android:textSize="@dimen/ts_18"
                android:textStyle="bold" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="1dp"
            android:layout_height="match_parent"
            android:layout_margin="1dp"
            android:background="@color/color_3D3D3D" />

        <com.owen.tvrecyclerview.widget.TvRecyclerView
            android:id="@+id/mChannelGridView"
            android:layout_width="@dimen/vs_250"
            android:layout_height="match_parent"
            android:divider="@null"
            android:fadeScrollbars="false"
            android:focusable="true"
            android:listSelector="@drawable/item_bg_selector_right"
            android:padding="@dimen/vs_20"
            android:scrollbars="none"
            app:tv_selectedItemIsCentered="true"
            app:tv_verticalSpacingWithMargins="10mm" />

        <LinearLayout
            android:layout_width="1dp"
            android:layout_height="match_parent"
            android:layout_margin="1dp"
            android:background="@color/color_3D3D3D" />

        <LinearLayout
            android:id="@+id/divLoadEpgright"
            android:layout_width="30dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:background="@drawable/shape_live_select"
            android:clickable="true"
            android:focusable="true"
            android:onClick="divLoadEpgR"
            android:orientation="vertical"
            android:paddingTop="@dimen/vs_15"
            android:paddingBottom="@dimen/vs_15">

            <TextView
                android:id="@+id/tv_arrow"
                android:layout_width="20dp"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:gravity="center"
                android:paddingBottom="5dp"
                android:shadowColor="@color/color_000000_80"
                android:shadowDx="0"
                android:shadowDy="0"
                android:shadowRadius="3"
                android:text="节 目"
                android:textColor="@color/color_FFFFFF"
                android:textSize="@dimen/ts_18"
                android:textStyle="bold" />

            <ImageView
                android:id="@+id/iv_arrow"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_gravity="center"
                android:gravity="center"
                android:padding="@dimen/vs_4"
                android:src="@drawable/scrollview" />

            <TextView
                android:id="@+id/tv_arrow2"
                android:layout_width="20dp"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:gravity="center"
                android:paddingTop="5dp"
                android:shadowColor="@color/color_000000_80"
                android:shadowDx="0"
                android:shadowDy="0"
                android:shadowRadius="3"
                android:text="信 息"
                android:textColor="@color/color_FFFFFF"
                android:textSize="@dimen/ts_18"
                android:textStyle="bold" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/divEPG"
            android:layout_width="@dimen/vs_460"
            android:layout_height="match_parent"
            android:layout_gravity="center_horizontal"
            android:orientation="horizontal"
            android:visibility="gone">

            <com.owen.tvrecyclerview.widget.TvRecyclerView
                android:id="@+id/mEpgDateGridView"
                android:layout_width="@dimen/vs_120"
                android:layout_height="match_parent"
                android:layout_marginStart="@dimen/vs_10"
                android:layout_marginLeft="@dimen/vs_10"
                android:layout_marginTop="@dimen/vs_10"
                android:layout_marginBottom="@dimen/vs_10"
                android:divider="@null"
                android:fadeScrollbars="false"
                android:focusable="true"
                android:listSelector="@drawable/item_bg_selector_right"
                android:paddingLeft="@dimen/vs_5"
                android:paddingTop="@dimen/vs_5"
                android:paddingBottom="@dimen/vs_5"
                android:textColor="@color/color_FFFFFF"
                app:tv_selectedItemIsCentered="true"
                app:tv_verticalSpacingWithMargins="10mm" />

            <com.owen.tvrecyclerview.widget.TvRecyclerView
                android:id="@+id/lv_epg"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="@dimen/vs_10"
                android:divider="@null"
                android:fadeScrollbars="false"
                android:focusable="true"
                android:listSelector="@drawable/item_bg_selector_right"
                android:padding="@dimen/vs_5"
                android:scrollbars="none" />

            <TextView
                android:id="@+id/txtNoEpg"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:gravity="center"
                android:text="暂无节目信息…"
                android:textColor="@color/color_FFFFFF"
                android:visibility="gone" />


        </LinearLayout>

    </LinearLayout>

    <LinearLayout
        android:id="@+id/tvRightSettingLayout"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="right"
        android:layout_margin="@dimen/vs_20"
        android:background="@drawable/bg_live"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:visibility="visible">

        <com.owen.tvrecyclerview.widget.TvRecyclerView
            android:id="@+id/mSettingItemView"
            android:layout_width="@dimen/vs_160"
            android:layout_height="match_parent"
            android:gravity="center"
            android:padding="@dimen/vs_15"
            android:visibility="visible"
            app:tv_selectedItemIsCentered="true"
            app:tv_verticalSpacingWithMargins="10mm" />

        <View
            android:layout_width="1mm"
            android:layout_height="match_parent"
            android:layout_gravity="center_horizontal"
            android:background="@color/color_3D3D3D" />

        <com.owen.tvrecyclerview.widget.TvRecyclerView
            android:id="@+id/mSettingGroupView"
            android:layout_width="@dimen/vs_180"
            android:layout_height="match_parent"
            android:gravity="center"
            android:padding="@dimen/vs_15"
            android:visibility="visible"
            app:tv_selectedItemIsCentered="true"
            app:tv_verticalSpacingWithMargins="10mm" />
    </LinearLayout>

    <TextView
        android:id="@+id/tvChannel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="right"
        android:layout_marginTop="30mm"
        android:layout_marginEnd="45mm"
        android:layout_marginRight="45mm"
        android:background="@drawable/shape_user_pause"
        android:gravity="center"
        android:padding="15mm"
        android:shadowColor="@color/color_FF000000"
        android:shadowDx="0"
        android:shadowDy="0"
        android:shadowRadius="3"
        android:text="Channel Text"
        android:textColor="@android:color/white"
        android:textSize="@dimen/ts_34"
        android:textStyle="bold"
        android:visibility="gone" />

    <TextView
        android:id="@+id/tvTime"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="right"
        android:layout_marginTop="5mm"
        android:layout_marginEnd="10mm"
        android:layout_marginRight="10mm"
        android:background="@android:color/transparent"
        android:gravity="center"
        android:shadowColor="@color/color_FF000000"
        android:shadowDx="0"
        android:shadowDy="0"
        android:shadowRadius="2"
        android:text="00:00 PM"
        android:textColor="@android:color/white"
        android:textSize="22mm"
        android:textStyle="bold"
        android:visibility="gone" />

    <TextView
        android:id="@+id/tvNetSpeed"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|right"
        android:layout_marginEnd="10mm"
        android:layout_marginRight="10mm"
        android:layout_marginBottom="5mm"
        android:background="@android:color/transparent"
        android:gravity="center"
        android:shadowColor="@color/color_FF000000"
        android:shadowDx="0"
        android:shadowDy="0"
        android:shadowRadius="2"
        android:text="0.00 MB/s"
        android:textColor="@android:color/white"
        android:textSize="22mm"
        android:textStyle="bold"
        android:visibility="gone" />

    <TextView
        android:id="@+id/tv_voluminfo"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_margin="20sp"
        android:background="#55000000"
        android:paddingLeft="30dp"
        android:paddingTop="10dp"
        android:paddingRight="30dp"
        android:paddingBottom="10dp"
        android:textColor="#fff"
        android:textSize="@dimen/dkplayer_controller_text_size"
        android:visibility="invisible" />

    <!--右下角网速显示-->
    <TextView
        android:id="@+id/tv_netspeedinfo"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|center|end"
        android:layout_marginRight="5dp"
        android:layout_marginBottom="5dp"
        android:textColor="#FFFFFFFF"
        android:visibility="invisible" />

    <!--频道序号显示-->
    <LinearLayout
        android:id="@+id/ll_epg"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:background="@drawable/shape_live_bg_bottom"
        android:gravity="bottom"
        android:paddingLeft="@dimen/vs_30"
        android:paddingRight="@dimen/vs_30"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/tv_logo"
            android:layout_width="@dimen/vs_400"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:layout_marginStart="@dimen/vs_60"
            android:layout_marginLeft="@dimen/vs_60"
            android:layout_marginTop="@dimen/vs_30"
            android:layout_marginEnd="@dimen/vs_30"
            android:layout_marginRight="@dimen/vs_30"
            android:layout_marginBottom="@dimen/vs_30"
            android:layout_weight="1"
            android:paddingTop="@dimen/vs_10"
            android:paddingBottom="@dimen/vs_10"
            app:srcCompat="@drawable/img_logo_placeholder" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:paddingTop="@dimen/vs_30"
            android:paddingLeft="@dimen/vs_30"
            android:paddingRight="@dimen/vs_30"
            android:paddingBottom="@dimen/vs_30">

            <LinearLayout
                android:id="@+id/ll_line1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="horizontal"
                android:paddingBottom="@dimen/vs_10">

                <View
                    android:id="@+id/redLine"
                    android:layout_width="@dimen/vs_6"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:layout_marginStart="@dimen/vs_20"
                    android:layout_marginLeft="@dimen/vs_20"
                    android:layout_marginTop="@dimen/vs_5"
                    android:layout_marginBottom="@dimen/vs_5"
                    android:background="?attr/color_theme" />

                <com.github.tvbox.osc.ui.tv.widget.MarqueeTextView
                    android:id="@+id/tv_current_program_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center|left"
                    android:layout_weight="1"
                    android:ellipsize="marquee"
                    android:fontFamily="sans-serif"
                    android:gravity="left"
                    android:marqueeRepeatLimit="marquee_forever"
                    android:paddingLeft="@dimen/vs_20"
                    android:paddingRight="@dimen/vs_20"
                    android:shadowColor="@color/color_FF000000"
                    android:shadowDx="0"
                    android:shadowDy="0"
                    android:shadowRadius="3"
                    android:singleLine="true"
                    android:text="No information"
                    android:textColor="@color/color_FFFFFF"
                    android:textSize="@dimen/ts_36"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tv_time"
                    android:layout_width="@dimen/vs_240"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center|right"
                    android:fontFamily="@font/advent_pro_extralight"
                    android:gravity="right"
                    android:maxLines="1"
                    android:paddingRight="@dimen/vs_60"
                    android:shadowColor="@color/color_FF000000"
                    android:shadowDx="0"
                    android:shadowDy="0"
                    android:shadowRadius="3"
                    android:text="00:00 AM"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_36"
                    android:textStyle="normal"
                    android:visibility="visible" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_line2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="horizontal"
                android:paddingBottom="@dimen/vs_5">

                <View
                    android:id="@+id/redLine"
                    android:layout_width="@dimen/vs_6"
                    android:layout_height="@dimen/vs_33"
                    android:layout_gravity="center"
                    android:layout_marginStart="@dimen/vs_20"
                    android:layout_marginLeft="@dimen/vs_20"
                    android:layout_marginTop="@dimen/vs_2"
                    android:layout_marginBottom="@dimen/vs_2"
                    android:background="@drawable/button_detail_collect" />

                <TextView
                    android:id="@+id/tv_current_program_time"
                    android:layout_width="@dimen/vs_160"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom|left"
                    android:layout_weight="1"
                    android:ellipsize="marquee"
                    android:fontFamily="sans-serif-condensed"
                    android:gravity="left"
                    android:maxLines="1"
                    android:paddingLeft="@dimen/vs_20"
                    android:shadowColor="@color/color_FF000000"
                    android:shadowDx="0"
                    android:shadowDy="0"
                    android:shadowRadius="3"
                    android:singleLine="true"
                    android:text="00:00 - 23:59"
                    android:textColor="@color/color_FFFFFF"
                    android:textSize="@dimen/ts_20"
                    android:textStyle="normal"
                    android:visibility="visible" />

                <TextView
                    android:id="@+id/tv_channel_number"
                    android:layout_width="@dimen/vs_100"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom|left"
                    android:layout_weight="1"
                    android:ellipsize="marquee"
                    android:fontFamily="sans-serif"
                    android:gravity="left"
                    android:maxLines="1"
                    android:paddingLeft="@dimen/vs_20"
                    android:paddingRight="@dimen/vs_20"
                    android:shadowColor="@color/color_FF000000"
                    android:shadowDx="0"
                    android:shadowDy="0"
                    android:shadowRadius="5"
                    android:singleLine="true"
                    android:text="1"
                    android:textColor="@color/color_FFFFFF"
                    android:textSize="@dimen/ts_20"
                    android:textStyle="bold"
                    android:visibility="visible" />

                <com.github.tvbox.osc.ui.tv.widget.MarqueeTextView
                    android:id="@+id/tv_channel_name"
                    android:layout_width="@dimen/vs_180"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom|center"
                    android:layout_weight="1"
                    android:ellipsize="marquee"
                    android:fontFamily="sans-serif"
                    android:gravity="left"
                    android:marqueeRepeatLimit="marquee_forever"
                    android:paddingRight="@dimen/vs_20"
                    android:shadowColor="@color/color_FF000000"
                    android:shadowDx="0"
                    android:shadowDy="0"
                    android:shadowRadius="5"
                    android:singleLine="true"
                    android:text="Channel 1"
                    android:textColor="@color/color_FFFFFF"
                    android:textSize="@dimen/ts_20"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/live_size"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom|center"
                    android:layout_marginEnd="@dimen/vs_20"
                    android:layout_marginRight="@dimen/vs_20"
                    android:layout_weight="1"
                    android:background="@drawable/channel_num_bg"
                    android:fontFamily="sans-serif"
                    android:gravity="center"
                    android:paddingLeft="@dimen/vs_8"
                    android:paddingTop="@dimen/vs_5"
                    android:paddingRight="@dimen/vs_8"
                    android:paddingBottom="@dimen/vs_5"
                    android:shadowColor="@color/color_FF000000"
                    android:shadowDx="0"
                    android:shadowDy="0"
                    android:shadowRadius="2"
                    android:text="1920 x 1080"
                    android:textColor="@color/color_FFFFFF"
                    android:textSize="@dimen/ts_15"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tv_source"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom|center"
                    android:layout_weight="1"
                    android:background="@drawable/channel_num_bg"
                    android:fontFamily="sans-serif"
                    android:gravity="center"
                    android:paddingLeft="@dimen/vs_8"
                    android:paddingTop="@dimen/vs_5"
                    android:paddingRight="@dimen/vs_8"
                    android:paddingBottom="@dimen/vs_5"
                    android:shadowColor="@color/color_FF000000"
                    android:shadowDx="0"
                    android:shadowDy="0"
                    android:shadowRadius="2"
                    android:text="源 1/1"
                    android:textColor="@color/color_FFFFFF"
                    android:textSize="@dimen/ts_15"
                    android:textStyle="bold" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_line3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="horizontal"
                android:paddingBottom="@dimen/vs_5">

                <View
                    android:id="@+id/redLine"
                    android:layout_width="@dimen/vs_6"
                    android:layout_height="@dimen/vs_33"
                    android:layout_gravity="center"
                    android:layout_marginStart="@dimen/vs_20"
                    android:layout_marginLeft="@dimen/vs_20"
                    android:layout_marginTop="@dimen/vs_2"
                    android:layout_marginBottom="@dimen/vs_2"
                    android:background="@drawable/button_detail_collect" />

                <TextView
                    android:id="@+id/tv_next_program_time"
                    android:layout_width="@dimen/vs_160"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom|left"
                    android:layout_weight="1"
                    android:ellipsize="marquee"
                    android:fontFamily="sans-serif-condensed"
                    android:gravity="left"
                    android:maxLines="1"
                    android:paddingLeft="@dimen/vs_20"
                    android:shadowColor="@color/color_FF000000"
                    android:shadowDx="0"
                    android:shadowDy="0"
                    android:shadowRadius="3"
                    android:singleLine="true"
                    android:text="00:00 - 23:59"
                    android:textColor="@color/color_FFFFFF"
                    android:textSize="@dimen/ts_20"
                    android:textStyle="normal"
                    android:visibility="visible" />

                <com.github.tvbox.osc.ui.tv.widget.MarqueeTextView
                    android:id="@+id/tv_next_program_name"
                    android:layout_width="@dimen/vs_440"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom|left"
                    android:layout_weight="1"
                    android:ellipsize="marquee"
                    android:fontFamily="sans-serif-condensed"
                    android:gravity="left"
                    android:marqueeRepeatLimit="marquee_forever"
                    android:paddingLeft="@dimen/vs_20"
                    android:paddingRight="@dimen/vs_20"
                    android:shadowColor="@color/color_FF000000"
                    android:shadowDx="0"
                    android:shadowDy="0"
                    android:shadowRadius="3"
                    android:singleLine="true"
                    android:text="No information"
                    android:textColor="@color/color_FFFFFF"
                    android:textSize="@dimen/ts_20"
                    android:textStyle="normal" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_seekbar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:paddingTop="@dimen/vs_5"
                android:visibility="gone">

                <TextView
                    android:id="@+id/curr_time"
                    android:layout_width="@dimen/vs_20_"
                    android:layout_height="wrap_content"
                    android:fontFamily="sans-serif-condensed"
                    android:gravity="center_vertical"
                    android:paddingLeft="@dimen/vs_20"
                    android:paddingEnd="@dimen/vs_10"
                    android:paddingRight="@dimen/vs_20"
                    android:shadowColor="@color/color_FF000000"
                    android:shadowDx="0"
                    android:shadowDy="0"
                    android:shadowRadius="3"
                    android:text="00:00:00"
                    android:textColor="@color/color_FFFFFF"
                    android:textSize="@dimen/ts_18" />

                <TextView
                    android:layout_width="10dp"
                    android:layout_height="wrap_content" />

                <SeekBar
                    android:id="@+id/seekBar"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom|center_vertical"
                    android:layout_weight="1"
                    android:background="@null"
                    android:focusable="true"
                    android:focusableInTouchMode="false"
                    android:max="1000"
                    android:maxHeight="@dimen/vs_2"
                    android:minHeight="@dimen/vs_2"
                    android:padding="@dimen/vs_1"
                    android:paddingStart="@dimen/vs_0"
                    android:paddingEnd="@dimen/vs_0"
                    android:progressDrawable="@drawable/shape_player_control_vod_seek"
                    android:thumb="@drawable/shape_player_control_vod_seek_thumb"
                    android:thumbOffset="@dimen/vs_0" />

                <TextView
                    android:layout_width="10dp"
                    android:layout_height="wrap_content" />

                <TextView
                    android:id="@+id/total_time"
                    android:layout_width="@dimen/vs_20_"
                    android:layout_height="wrap_content"
                    android:fontFamily="sans-serif-condensed"
                    android:gravity="center_vertical"
                    android:paddingStart="@dimen/vs_10"
                    android:paddingLeft="@dimen/vs_20"
                    android:paddingRight="@dimen/vs_60"
                    android:shadowColor="@color/color_FF000000"
                    android:shadowDx="0"
                    android:shadowDy="0"
                    android:shadowRadius="3"
                    android:text="00:00:00"
                    android:textColor="@color/color_FFFFFF"
                    android:textSize="@dimen/ts_18" />
            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

    <TextView
        android:id="@+id/tv_shownum"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="end|top"
        android:layout_margin="20dp"
        android:paddingTop="8dp" />

    <!--时间显示-->
    <TextView
        android:id="@+id/tv_showTime"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="end|top"
        android:layout_margin="20dp"
        android:paddingTop="8dp" />

</FrameLayout>