<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_focused="true">
        <shape android:shape="rectangle">
            <corners android:radius="@dimen/vs_5" />
            <gradient
                android:angle="180"
                android:endColor="#ff772d"
                android:startColor="#ffc41c" />
        </shape>
    </item>
    <item android:state_focused="false">
        <shape>
            <corners android:radius="@dimen/vs_5" />
            <solid android:color="#3A3D41" />
        </shape>
    </item>
</selector>