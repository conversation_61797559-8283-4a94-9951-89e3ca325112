<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    android:orientation="vertical"
    android:clipChildren="false"
    android:clipToPadding="false"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    android:layout_margin="@dimen/vs_5"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto" >

    <ScrollView
        android:layout_width="fill_parent"
        android:layout_height="fill_parent"
        android:overScrollMode="never"
        android:scrollbars="none">

        <!--调整左右边距  首页轮播图
            android:paddingLeft="@dimen/vs_10"左边距
            android:paddingRight="@dimen/vs_10"右边距
            调底下的，不是调这个备注信息
        -->
        <LinearLayout
            android:orientation="vertical"
            android:paddingLeft="@dimen/vs_20"
            android:paddingRight="@dimen/vs_20"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content" >


            <!--
                  <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/bannerlist"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">
                </androidx.recyclerview.widget.RecyclerView>



                        -->


                <com.owen.tvrecyclerview.widget.TvRecyclerView
                    android:orientation="horizontal"
                    android:id="@+id/bannerlist"
                    android:padding="@dimen/vs_15"
                    android:clipChildren="false"
                    android:clipToPadding="false"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:tv_horizontalSpacingWithMargins="@dimen/vs_10"
                    app:tv_layoutManager="V7LinearLayoutManager"
                    app:tv_selectedItemIsCentered="true"
                    app:tv_verticalSpacingWithMargins="@dimen/vs_10" />


            <LinearLayout
                android:id="@+id/tvUserHome"
                android:paddingLeft="@dimen/vs_2"
                android:paddingRight="@dimen/vs_2"
                android:paddingTop="@dimen/vs_10"
                android:paddingBottom="@dimen/vs_10"
                android:focusable="false"
                android:focusableInTouchMode="false"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:layout_width="fill_parent"
                android:layout_height="@dimen/vs_180"
                android:orientation="horizontal">

                <FrameLayout
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:id="@+id/ll_go_play"
                    android:background="@drawable/shape_user3_focus"
                    android:padding="@dimen/vs_5"
                    android:focusable="true"
                    android:clickable="false"
                    android:layout_width="0.0dip"
                    android:layout_height="fill_parent"
                    android:layout_margin="@dimen/vs_5"
                    android:minWidth="@dimen/vs_150"
                    android:layout_weight="1.0">

                    <ImageView
                        android:layout_width="fill_parent"
                        android:layout_height="fill_parent"
                        android:layout_margin="@dimen/vs_3"
                        android:alpha="0.9"
                        app:srcCompat="@drawable/shape_user2_focus" />

                    <ImageView
                        android:layout_gravity="bottom|center|right"
                        android:background="@drawable/ic_history"
                        android:layout_marginBottom="@dimen/vs_15"
                        android:layout_marginRight="@dimen/vs_5"
                        android:layout_width="45.0sp"
                        android:layout_height="45.0sp"  />

                    <LinearLayout
                        android:layout_gravity="center"
                        android:orientation="vertical"
                        android:paddingLeft="@dimen/sp_14"
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content" >

                        <TextView
                            android:textSize="@dimen/ts_24"
                            android:textColor="@color/color_FFFFFF"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="继续观看"
                            android:maxLines="1"
                            android:fontFamily="sans-serif-black"  />

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content" >

                            <TextView
                            android:id="@+id/tv_video_name"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginLeft="1dp"
                            android:textColor="@color/color_FFFFFF"
                            android:textSize="@dimen/ts_22"
                            android:text="暂无观看记录"/>

                        </LinearLayout>

                    </LinearLayout>

                </FrameLayout>

                <FrameLayout
                    android:layout_gravity="center"
                    android:orientation="horizontal"
                    android:id="@+id/tvFavorite"
                    android:background="@drawable/shape_user3_focus"
                    android:padding="@dimen/vs_5"
                    android:focusable="true"
                    android:clickable="false"
                    android:layout_width="0.0dip"
                    android:layout_height="fill_parent"
                    android:layout_margin="@dimen/vs_5"
                    android:minWidth="@dimen/vs_150"
                    android:layout_weight="1.0" >

                    <ImageView
                        android:id="@+id/hm_fav"
                        android:layout_width="fill_parent"
                        android:layout_height="fill_parent"
                        android:layout_margin="@dimen/vs_3"
                        android:alpha="0.9"
                        app:srcCompat="@drawable/shape_user1_focus" />

                    <ImageView
                        android:layout_gravity="bottom|center|right"
                        android:background="@drawable/ic_collect"
                        android:layout_marginBottom="@dimen/vs_15"
                        android:layout_marginRight="@dimen/vs_5"
                        android:layout_width="45.0sp"
                        android:layout_height="45.0sp"  />

                    <LinearLayout
                        android:layout_gravity="center"
                        android:orientation="vertical"
                        android:paddingLeft="@dimen/sp_14"
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content" >

                        <TextView
                            android:textSize="@dimen/ts_24"
                            android:textStyle="normal"
                            android:textColor="@color/color_FFFFFF_90"
                            android:gravity="center"
                            android:padding="@dimen/vs_3"
                            android:focusable="false"
                            android:focusableInTouchMode="false"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="收藏"
                            android:fontFamily="sans-serif-black"  />

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content" >

                            <TextView
                                android:textSize="@dimen/ts_22"
                                android:textColor="@color/color_FFFFFF"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="@dimen/vs_3"
                                android:text="喜欢的东西都在这里"
                                android:maxLines="1"
                                android:fontFamily="sans-serif-black" />

                            <TextView
                                android:textSize="@dimen/vs_20"
                                android:textColor="@color/color_FFFFFF"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="@dimen/dp_10"  />

                        </LinearLayout>

                    </LinearLayout>

                </FrameLayout>

                <FrameLayout
                    android:layout_gravity="center"
                    android:orientation="horizontal"
                    android:id="@+id/tvPush"
                    android:background="@drawable/shape_user3_focus"
                    android:padding="@dimen/vs_5"
                    android:focusable="true"
                    android:clickable="false"
                    android:layout_width="0.0dip"
                    android:layout_height="fill_parent"
                    android:layout_margin="@dimen/vs_5"
                    android:minWidth="@dimen/vs_150"
                    android:layout_weight="1.0" >

                    <ImageView
                        android:id="@+id/hm_push"
                        android:layout_width="fill_parent"
                        android:layout_height="fill_parent"
                        android:layout_margin="@dimen/vs_3"
                        android:alpha="0.9"
                        app:srcCompat="@drawable/shape_user4_focus" />

                    <ImageView
                        android:layout_gravity="bottom|center|right"
                        android:background="@drawable/ic_saoma"
                        android:layout_marginBottom="@dimen/vs_15"
                        android:layout_marginRight="@dimen/vs_5"
                        android:layout_width="45.0sp"
                        android:layout_height="45.0sp"  />

                    <LinearLayout
                        android:layout_gravity="center"
                        android:orientation="vertical"
                        android:paddingLeft="@dimen/sp_14"
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content" >

                        <TextView
                            android:textSize="@dimen/ts_24"
                            android:textStyle="normal"
                            android:textColor="@color/color_FFFFFF_90"
                            android:gravity="center"
                            android:padding="@dimen/vs_3"
                            android:focusable="false"
                            android:focusableInTouchMode="false"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="推送"
                            android:fontFamily="sans-serif-black"  />

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content" >

                            <TextView
                                android:textSize="@dimen/ts_22"
                                android:textColor="@color/color_FFFFFF"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="@dimen/vs_3"
                                android:text="手机扫一扫搜索"
                                android:maxLines="1"
                                android:fontFamily="sans-serif-black" />

                            <TextView
                                android:textSize="@dimen/vs_20"
                                android:textColor="@color/color_FFFFFF"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="@dimen/dp_10" />

                        </LinearLayout>

                    </LinearLayout>

                </FrameLayout>

                <FrameLayout
                    android:orientation="horizontal"
                    android:id="@+id/tvLive"
                    android:background="@drawable/shape_user3_focus"
                    android:padding="@dimen/vs_5"
                    android:focusable="true"
                    android:clickable="false"
                    android:layout_width=".0dip"
                    android:layout_height="fill_parent"
                    android:layout_margin="@dimen/vs_5"
                    android:minWidth="@dimen/vs_150"
                    android:layout_weight="1.0">

                    <ImageView
                        android:id="@+id/hm_live"
                        android:layout_width="fill_parent"
                        android:layout_height="fill_parent"
                        android:layout_margin="@dimen/vs_3"
                        android:alpha="0.9"
                        app:srcCompat="@drawable/shape_user5_focus" />

                    <ImageView
                        android:layout_gravity="bottom|center|right"
                        android:background="@drawable/ic_zhibo"
                        android:layout_marginBottom="@dimen/vs_15"
                        android:layout_marginRight="@dimen/vs_5"
                        android:layout_width="45.0sp"
                        android:layout_height="45.0sp" />

                    <LinearLayout
                        android:layout_gravity="center"
                        android:orientation="vertical"
                        android:paddingLeft="@dimen/sp_14"
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content" >

                        <TextView
                            android:textSize="@dimen/ts_24"
                            android:textStyle="normal"
                            android:textColor="@color/color_FFFFFF_90"
                            android:gravity="center"
                            android:padding="@dimen/vs_3"
                            android:focusable="false"
                            android:focusableInTouchMode="false"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="直播"
                            android:fontFamily="sans-serif-black" />

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content" >

                            <TextView
                                android:textSize="@dimen/ts_22"
                                android:textColor="@color/color_FFFFFF"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="@dimen/vs_3"
                                android:text="央视、卫视一网打尽"
                                android:maxLines="1"
                                android:fontFamily="sans-serif-black"  />

                            <TextView
                                android:textSize="@dimen/vs_20"
                                android:textColor="@color/color_FFFFFF"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="@dimen/dp_10"  />

                        </LinearLayout>

                    </LinearLayout>

                </FrameLayout>

            </LinearLayout>


            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/vs_5"
                android:text="为你推荐"
                android:textColor="@color/white"
                android:textSize="@dimen/ts_34" />

            <com.owen.tvrecyclerview.widget.TvRecyclerView
                android:id="@+id/tvHotList"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/vs_40"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:orientation="horizontal"
                android:padding="@dimen/vs_10"
                app:tv_horizontalSpacingWithMargins="@dimen/vs_10"
                app:tv_layoutManager="V7LinearLayoutManager"
                app:tv_selectedItemIsCentered="true"
                app:tv_verticalSpacingWithMargins="@dimen/vs_10" />

        </LinearLayout>

    </ScrollView>

</LinearLayout>