<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <ImageView
            android:id="@+id/iv_image"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:src="@drawable/lanch_bg2"
            android:scaleType="fitXY" />
        <ImageView
            android:layout_gravity="center"
            android:id="@id/ivQRCode"
            android:visibility="gone"
            android:layout_width="wrap_content"
            android:src="@drawable/lanch_bg2"
            android:layout_height="wrap_content" />
        <LinearLayout
            android:gravity="center"
            android:layout_gravity="right"
            android:id="@+id/ll_ok_tiao"
            android:paddingLeft="@dimen/vs_10"
            android:paddingTop="@dimen/vs_5"
            android:paddingRight="@dimen/vs_10"
            android:paddingBottom="@dimen/vs_5"
            android:visibility="gone"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/ts_50"
            android:layout_marginRight="@dimen/vs_30">
            <LinearLayout
                android:gravity="center"
                android:id="@+id/tv_TTime"
                android:background="@drawable/shape_user_search"
                android:paddingLeft="@dimen/vs_15"
                android:paddingTop="@dimen/vs_5"
                android:paddingRight="@dimen/vs_15"
                android:paddingBottom="@dimen/vs_5"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/vs_35"
                android:layout_marginRight="@dimen/vs_10">
                <TextView
                    android:textSize="@dimen/vs_15"
                    android:textColor="@color/color_FFFFFF"
                    android:gravity="center"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="按" />
                <TextView
                    android:textSize="@dimen/vs_15"
                    android:textColor="@color/black"
                    android:gravity="center"
                    android:background="@drawable/shape_bg_black_text_white2"
                    android:paddingLeft="@dimen/vs_5"
                    android:paddingTop="@dimen/vs_2"
                    android:paddingRight="@dimen/vs_5"
                    android:paddingBottom="@dimen/vs_2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/vs_5"
                    android:layout_marginRight="@dimen/vs_5"
                    android:text="OK" />
                <TextView
                    android:textSize="@dimen/vs_15"
                    android:textColor="@color/color_FFFFFF"
                    android:gravity="center"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="跳过" />
            </LinearLayout>
            <LinearLayout
                android:gravity="center"
                android:layout_gravity="right"
                android:id="@+id/llDTime"
                android:background="@drawable/shape_user_search"
                android:paddingLeft="@dimen/vs_15"
                android:paddingTop="@dimen/vs_5"
                android:paddingRight="@dimen/vs_15"
                android:paddingBottom="@dimen/vs_5"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/vs_35">
                <TextView
                    android:textSize="@dimen/vs_15"
                    android:textColor="@color/color_FFFFFF"
                    android:gravity="center"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="广告" />
                <TextView
                    android:textSize="@dimen/vs_15"
                    android:textColor="@color/color_FFB800"
                    android:gravity="center"
                    android:id="@+id/tv_start"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/vs_5"
                    android:text="5" />
            </LinearLayout>
        </LinearLayout>
    </FrameLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
