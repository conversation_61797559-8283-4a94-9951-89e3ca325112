<?xml version="1.0" encoding="utf-8"?>
<LinearLayout  xmlns:android="http://schemas.android.com/apk/res/android"
    android:background="@drawable/shape_user3_focus"
    android:padding="@dimen/vs_5"
    android:focusable="true"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_marginLeft="@dimen/dp_10"
    android:layout_marginRight="@dimen/dp_10"
    android:id="@+id/item_lbt"
    android:baselineAligned="false">
    <!-- 首页轮播图列表 -->
    <FrameLayout
        android:id="@+id/dsadsad"
        android:layout_width="568mm"
        android:layout_height="@dimen/vs_280">
        <ImageView
            android:id="@id/ivThumb"
            android:padding="@dimen/vs_1"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="fitXY" />
        <LinearLayout
            android:layout_gravity="right"
            android:orientation="horizontal"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">
            <TextView
                android:textSize="@dimen/ts_20"
                android:textColor="@android:color/white"
                android:ellipsize="marquee"
                android:gravity="center"
                android:id="@id/tvRate"
                android:background="@drawable/shape_thumb_year"
                android:paddingLeft="@dimen/vs_10"
                android:paddingTop="@dimen/vs_2"
                android:paddingRight="@dimen/vs_10"
                android:paddingBottom="@dimen/vs_2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="右滑更多"
                android:singleLine="true"
                android:marqueeRepeatLimit="marquee_forever" />
        </LinearLayout>
        <LinearLayout
            android:layout_gravity="bottom"
            android:orientation="horizontal"
            android:background="@drawable/shape_user6_focus"
            android:padding="@dimen/vs_1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/vs_1">
            <TextView
                android:textSize="@dimen/ts_22"
                android:textColor="@android:color/white"
                android:ellipsize="marquee"
                android:id="@id/tvName"
                android:padding="@dimen/vs_5"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="少年歌行"
                android:singleLine="true"
                android:marqueeRepeatLimit="marquee_forever" />
            <TextView
                android:textSize="@dimen/ts_20"
                android:textColor="@color/colorTextNormal"
                android:ellipsize="marquee"
                android:gravity="center"
                android:id="@id/tvNote"
                android:paddingLeft="@dimen/vs_5"
                android:paddingRight="@dimen/vs_5"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/vs_2"
                android:layout_marginRight="@dimen/vs_2"
                android:text="热播"
                android:singleLine="true"
                android:marqueeRepeatLimit="marquee_forever" />
        </LinearLayout>
    </FrameLayout>
</LinearLayout>
