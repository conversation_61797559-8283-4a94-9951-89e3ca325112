<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:background="@drawable/shape_user_search"
    android:padding="@dimen/vs_5"
    android:focusable="true"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:id="@+id/item_user_shop_group"
    android:baselineAligned="false">

    <!-- 积分商城列表 -->

    <LinearLayout
        android:gravity="center"
        android:orientation="vertical"
        android:id="@+id/ll_VIP_year"
        android:padding="@dimen/vs_15"
        android:focusable="true"
        android:layout_width="@dimen/vs_340"
        android:layout_height="wrap_content">
        <TextView
            android:textSize="@dimen/ts_40"
            android:textColor="#FFF9C690"
            android:gravity="center"
            android:layout_gravity="center"
            android:id="@+id/tv_shop_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="商品标题" />
        <LinearLayout
            android:gravity="center"
            android:paddingLeft="@dimen/vs_15"
            android:paddingRight="@dimen/vs_15"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/vs_20">
            <LinearLayout
                android:orientation="vertical"
                android:background="@drawable/shape_user_search"
                android:paddingLeft="@dimen/vs_25"
                android:paddingTop="@dimen/vs_8"
                android:paddingRight="@dimen/vs_25"
                android:paddingBottom="@dimen/vs_8"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">
                <ImageView
                    android:layout_gravity="center"
                    android:id="@+id/iv_shop_img"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_tv__img" />
                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_vuser_vip" />
            </LinearLayout>
            <View
                android:layout_width="@dimen/vs_15"
                android:layout_height="1dp" />
            <LinearLayout
                android:orientation="vertical"
                android:background="@drawable/shape_user_search"
                android:paddingLeft="@dimen/vs_25"
                android:paddingTop="@dimen/vs_8"
                android:paddingRight="@dimen/vs_25"
                android:paddingBottom="@dimen/vs_8"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">
                <ImageView
                    android:layout_gravity="center"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_tv__img" />
                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_huore" />
            </LinearLayout>
        </LinearLayout>
        <LinearLayout
            android:layout_gravity="bottom"
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/vs_20">
            <TextView
                android:textSize="@dimen/vs_30"
                android:textColor="@color/color_FFFFFF"
                android:layout_gravity="center"
                android:id="@+id/tv_shop_price"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="200/积分" />
            <TextView
                android:textSize="@dimen/vs_20"
                android:textColor="@color/color_FFFFFF"
                android:layout_gravity="center"
                android:id="@+id/tv_shop_remarks"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/vs_15"
                android:text="点亮会员独特标识"
                android:maxLines="8"
                android:lines="2" />
        </LinearLayout>
    </LinearLayout>
</LinearLayout>
