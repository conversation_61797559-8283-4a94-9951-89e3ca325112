<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="@dimen/vs_60"
    android:background="@drawable/tv_category_button"
    android:clickable="true"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:orientation="horizontal"
    android:gravity="center_vertical"
    android:paddingLeft="@dimen/vs_25"
    android:paddingRight="@dimen/vs_25"
    android:layout_marginBottom="@dimen/vs_10">

    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:focusable="false"
        android:gravity="center_vertical"
        android:textColor="@color/tv_text_primary"
        android:textSize="@dimen/ts_28"
        android:textStyle="bold" />

    <ImageView
        android:id="@+id/tvFilter"
        android:layout_width="@dimen/vs_30"
        android:layout_height="@dimen/vs_30"
        android:layout_gravity="center"
        android:layout_marginStart="@dimen/vs_3"
        android:layout_marginLeft="@dimen/vs_3"
        android:src="@drawable/icon_filter"
        android:visibility="gone" />
</LinearLayout>